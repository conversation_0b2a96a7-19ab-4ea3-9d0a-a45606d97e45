<template>
  <svg 
    :width="size" 
    :height="size" 
    viewBox="0 0 100 100" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    class="electric-car-icon"
  >
    <!-- 车身主体 -->
    <path
      d="M15 65 Q15 58 22 58 L78 58 Q85 58 85 65 L85 75 Q85 82 78 82 L22 82 Q15 82 15 75 Z"
      :fill="bodyColor"
      stroke="currentColor"
      stroke-width="1.5"
    />
    
    <!-- 车顶 -->
    <path
      d="M25 58 Q25 45 35 40 L65 40 Q75 45 75 58"
      :fill="bodyColor"
      stroke="currentColor"
      stroke-width="1.5"
      opacity="0.9"
    />
    
    <!-- 前挡风玻璃 -->
    <path
      d="M35 45 Q40 42 45 42 L55 42 Q60 42 65 45 L65 55 L35 55 Z"
      fill="rgba(135, 206, 235, 0.6)"
      stroke="currentColor"
      stroke-width="1"
    />
    
    <!-- 车轮 -->
    <circle
      cx="30"
      cy="75"
      r="8"
      fill="#2c3e50"
      stroke="currentColor"
      stroke-width="1"
    />
    <circle
      cx="70"
      cy="75"
      r="8"
      fill="#2c3e50"
      stroke="currentColor"
      stroke-width="1"
    />
    
    <!-- 轮胎内圈 -->
    <circle cx="30" cy="75" r="5" fill="#34495e" />
    <circle cx="70" cy="75" r="5" fill="#34495e" />
    
    <!-- 车灯 -->
    <ellipse
      cx="85"
      cy="65"
      rx="3"
      ry="4"
      fill="#f1c40f"
      opacity="0.8"
    />
    <ellipse
      cx="15"
      cy="65"
      rx="3"
      ry="4"
      fill="#e74c3c"
      opacity="0.8"
    />
    
    <!-- 电动标识 -->
    <g v-if="showElectricSymbol">
      <!-- 闪电符号 -->
      <path
        d="M48 25 L52 25 L50 35 L54 35 L46 45 L42 45 L44 35 L40 35 Z"
        fill="#f1c40f"
        stroke="#e67e22"
        stroke-width="0.5"
      />
    </g>
    
    <!-- 天线 -->
    <line
      x1="50"
      y1="40"
      x2="50"
      y2="32"
      stroke="currentColor"
      stroke-width="1.5"
      stroke-linecap="round"
    />
    <circle cx="50" cy="30" r="2" fill="#e74c3c" />
  </svg>
</template>

<script setup lang="ts">
interface Props {
  size?: number
  bodyColor?: string
  showElectricSymbol?: boolean
}

withDefaults(defineProps<Props>(), {
  size: 32,
  bodyColor: '#3498db',
  showElectricSymbol: true
})
</script>

<style scoped>
.electric-car-icon {
  transition: all 0.3s ease;
}

.electric-car-icon:hover {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}
</style>