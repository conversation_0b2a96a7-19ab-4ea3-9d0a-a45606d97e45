<template>
  <div class="vehicle-map-container">
    <div class="map-background" ref="mapRef">
      <!-- 车辆图标 -->
      <div
        v-for="vehicle in vehicleStore.vehicles"
        :key="vehicle.id"
        class="vehicle-icon"
        :class="[
          `status-${vehicle.status}`,
          { 'low-battery': vehicle.battery < 20 }
        ]"
        :style="{
          left: `${vehicle.positionX}%`,
          top: `${vehicle.positionY}%`
        }"
        @click="showVehicleInfo(vehicle)"
      >
        <ElectricCarIcon 
          :size="32"
          :body-color="getVehicleColor(vehicle.status)"
          :show-electric-symbol="vehicle.battery > 20"
        />
        <BatteryIndicator :percentage="vehicle.battery" />
      </div>
    </div>

    <!-- 图例 -->
    <div class="map-legend">
      <div class="legend-item">
        <div class="legend-icon status-available"></div>
        <span>可用</span>
      </div>
      <div class="legend-item">
        <div class="legend-icon status-rented"></div>
        <span>已租用</span>
      </div>
      <div class="legend-item">
        <div class="legend-icon status-faulty"></div>
        <span>故障</span>
      </div>
    </div>

    <!-- 车辆信息弹窗 -->
    <el-dialog
      v-model="showVehicleDialog"
      :title="`车辆信息 - ${selectedVehicle?.licensePlate}`"
      width="400px"
    >
      <div v-if="selectedVehicle" class="vehicle-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="车牌号">
            {{ selectedVehicle.licensePlate }}
          </el-descriptions-item>
          <el-descriptions-item label="电量">
            <el-progress
              :percentage="selectedVehicle.battery"
              :color="getBatteryColor(selectedVehicle.battery)"
            />
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedVehicle.status)">
              {{ getStatusText(selectedVehicle.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="位置">
            X: {{ selectedVehicle.positionX.toFixed(1) }}%, Y: {{ selectedVehicle.positionY.toFixed(1) }}%
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="vehicle-actions" v-if="selectedVehicle.status === 'available'">
          <el-button
            type="primary"
            size="large"
            :loading="rentLoading"
            @click="handleRent"
            style="width: 100%; margin-top: 20px;"
          >
            一键租车
          </el-button>
        </div>
        
        <div class="vehicle-actions" v-else-if="selectedVehicle.status === 'faulty'">
          <el-button
            type="warning"
            size="large"
            @click="goToFaultReport"
            style="width: 100%; margin-top: 20px;"
          >
            查看故障详情
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useVehicleStore } from '@/stores/vehicle'
import { useUserStore } from '@/stores/user'
import { type Vehicle } from '@/api/vehicle'
import { startRent } from '@/api/rent'
import ElectricCarIcon from './ElectricCarIcon.vue'
import BatteryIndicator from './BatteryIndicator.vue'

const router = useRouter()
const vehicleStore = useVehicleStore()
const userStore = useUserStore()

const mapRef = ref()
const showVehicleDialog = ref(false)
const selectedVehicle = ref<Vehicle | null>(null)
const rentLoading = ref(false)
let updateTimer: NodeJS.Timeout | null = null

const showVehicleInfo = (vehicle: Vehicle) => {
  selectedVehicle.value = vehicle
  showVehicleDialog.value = true
}

const getVehicleColor = (status: Vehicle['status']) => {
  switch (status) {
    case 'available': return '#67c23a'
    case 'rented': return '#e6a23c'
    case 'faulty': return '#f56c6c'
    default: return '#909399'
  }
}

const getBatteryColor = (battery: number) => {
  if (battery >= 60) return '#67c23a'
  if (battery >= 30) return '#e6a23c'
  return '#f56c6c'
}

const getStatusType = (status: Vehicle['status']) => {
  switch (status) {
    case 'available': return 'success'
    case 'rented': return 'warning'
    case 'faulty': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: Vehicle['status']) => {
  switch (status) {
    case 'available': return '可用'
    case 'rented': return '已租用'
    case 'faulty': return '故障'
    default: return '未知'
  }
}

const handleRent = async () => {
  if (!selectedVehicle.value) return

  // 检查用户是否已缴纳押金
  if (!userStore.userInfo.deposit || userStore.userInfo.deposit <= 0) {
    ElMessage.warning('请先缴纳押金才能租车')
    router.push('/user/wallet')
    return
  }

  rentLoading.value = true
  try {
    const order = await startRent({ vehicleId: selectedVehicle.value.id })
    
    ElMessage.success('租车成功，开始计费')
    userStore.setCurrentOrder(order)
    vehicleStore.updateVehicleStatus(selectedVehicle.value.id, 'rented')
    
    showVehicleDialog.value = false
  } catch (error) {
    console.error('租车失败:', error)
  } finally {
    rentLoading.value = false
  }
}

const goToFaultReport = () => {
  showVehicleDialog.value = false
  router.push('/fault-report')
}

const startVehicleUpdates = () => {
  // 立即加载一次
  vehicleStore.updateVehicles()
  
  // 每5秒更新一次车辆状态
  updateTimer = setInterval(() => {
    vehicleStore.updateVehicles()
  }, 5000)
}

const stopVehicleUpdates = () => {
  if (updateTimer) {
    clearInterval(updateTimer as any)
    updateTimer = null
  }
}

onMounted(() => {
  startVehicleUpdates()
})

onUnmounted(() => {
  stopVehicleUpdates()
})
</script>

<style scoped>
.vehicle-map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.map-background {
  width: 100%;
  height: 100%;
  background: 
    /* 道路网格 */
    linear-gradient(90deg, rgba(200, 200, 200, 0.3) 1px, transparent 1px),
    linear-gradient(rgba(200, 200, 200, 0.3) 1px, transparent 1px),
    /* 主要道路 */
    linear-gradient(90deg, rgba(64, 158, 255, 0.2) 2px, transparent 2px),
    linear-gradient(rgba(64, 158, 255, 0.2) 2px, transparent 2px),
    /* 背景色 */
    linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 50%, #e8f5e8 100%);
  background-size: 
    40px 40px,
    40px 40px,
    200px 200px,
    200px 200px,
    100% 100%;
  background-position: 
    0 0,
    0 0,
    100px 100px,
    100px 100px,
    0 0;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.05);
}

.vehicle-icon {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(5px);
}

.vehicle-icon:hover {
  transform: translate(-50%, -50%) scale(1.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
  background: rgba(255, 255, 255, 0.95);
}

.vehicle-icon.status-available {
  color: #67c23a;
  border: 2px solid rgba(103, 194, 58, 0.3);
}

.vehicle-icon.status-rented {
  color: #e6a23c;
  border: 2px solid rgba(230, 162, 60, 0.3);
}

.vehicle-icon.status-faulty {
  color: #f56c6c;
  border: 2px solid rgba(245, 108, 108, 0.3);
  animation: pulse 2s infinite;
}

.vehicle-icon.low-battery {
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.6; }
}

@keyframes pulse {
  0% { box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3); }
  50% { box-shadow: 0 4px 16px rgba(245, 108, 108, 0.6); }
  100% { box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3); }
}

.map-legend {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}

.legend-icon.status-available {
  background-color: #67c23a;
}

.legend-icon.status-rented {
  background-color: #e6a23c;
}

.legend-icon.status-faulty {
  background-color: #f56c6c;
}

.vehicle-info {
  text-align: center;
}

.vehicle-actions {
  margin-top: 20px;
}
</style>