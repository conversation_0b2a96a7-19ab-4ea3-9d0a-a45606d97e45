<template>
  <div class="image-upload">
    <el-upload
      class="avatar-uploader"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :show-file-list="false"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload"
      :loading="loading"
      accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
    >
      <img v-if="imageUrl" :src="displayImageUrl" class="avatar" />
      <div v-else class="avatar-placeholder">
        <DefaultAvatar 
          v-if="showDefaultAvatar"
          :name="defaultName"
          :size="defaultAvatarSize"
        />
        <div v-else class="upload-icon">
          <el-icon>
            <i class="el-icon-plus"></i>
          </el-icon>
          <div class="upload-text">点击上传</div>
        </div>
      </div>
    </el-upload>
    <div class="upload-tips" v-if="showTips">
      <p>支持JPG、PNG、GIF、WEBP格式</p>
      <p>文件大小不超过5MB</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import DefaultAvatar from './DefaultAvatar.vue'

interface Props {
  modelValue?: string
  uploadType?: 'avatar' | 'image'
  showTips?: boolean
  width?: string
  height?: string
  defaultName?: string
  showDefaultAvatar?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'success', url: string): void
}

const props = withDefaults(defineProps<Props>(), {
  uploadType: 'image',
  showTips: true,
  width: '178px',
  height: '178px',
  defaultName: '用户',
  showDefaultAvatar: false
})

const emit = defineEmits<Emits>()

const userStore = useUserStore()
const loading = ref(false)

const defaultAvatarSize = computed(() => {
  const size = parseInt(props.width.replace('px', ''))
  return Math.min(size * 0.6, 120)
})

const displayImageUrl = computed(() => {
  if (!props.modelValue) return ''
  
  // 如果已经是完整URL，直接返回
  if (props.modelValue.startsWith('http')) {
    return props.modelValue
  }
  
  // 如果已经包含/api前缀，直接返回
  if (props.modelValue.startsWith('/api/')) {
    return props.modelValue
  }
  
  // 如果是相对路径，添加API基础路径
  const baseUrl = import.meta.env.VITE_API_BASE_URL || '/api'
  return `${baseUrl}${props.modelValue}`
})

const imageUrl = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
})

const uploadUrl = computed(() => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || '/api'
  return `${baseUrl}/upload/${props.uploadType}`
})

const uploadHeaders = computed(() => {
  const token = userStore.token
  return token ? { Authorization: `Bearer ${token}` } : {}
})

const beforeUpload = (file: File) => {
  const isImage = /^image\/(jpeg|jpg|png|gif|webp)$/i.test(file.type)
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传JPG、PNG、GIF、WEBP格式的图片!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('上传图片大小不能超过 5MB!')
    return false
  }
  
  loading.value = true
  return true
}

const handleSuccess = (response: any) => {
  loading.value = false
  if (response.code === 200) {
    const url = response.data
    imageUrl.value = url
    emit('success', url)
    ElMessage.success('上传成功')
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const handleError = (error: any) => {
  loading.value = false
  console.error('上传失败:', error)
  ElMessage.error('上传失败，请重试')
}
</script>

<style scoped>
.image-upload {
  text-align: center;
}

.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-placeholder {
  width: v-bind(width);
  height: v-bind(height);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.upload-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
}

.upload-icon .el-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
  color: #8c939d;
}

.avatar {
  width: v-bind(width);
  height: v-bind(height);
  display: block;
  object-fit: cover;
}

.upload-tips {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

.upload-tips p {
  margin: 2px 0;
}

:deep(.el-loading-mask) {
  border-radius: 6px;
}
</style>