<template>
  <div 
    class="default-avatar" 
    :style="{ 
      width: size + 'px', 
      height: size + 'px',
      backgroundColor: bgColor,
      fontSize: fontSize + 'px'
    }"
  >
    {{ displayText }}
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  name?: string
  size?: number
}

const props = withDefaults(defineProps<Props>(), {
  name: '用户',
  size: 40
})

// 根据名字生成颜色
const colors = [
  '#f56565', '#ed8936', '#ecc94b', '#48bb78', '#38b2ac',
  '#4299e1', '#0bc5ea', '#9f7aea', '#ed64a6', '#667eea'
]

const bgColor = computed(() => {
  const hash = props.name.split('').reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc)
  }, 0)
  return colors[Math.abs(hash) % colors.length]
})

const displayText = computed(() => {
  if (!props.name) return '用'
  
  // 如果是中文，取第一个字符
  if (/[\u4e00-\u9fa5]/.test(props.name)) {
    return props.name.charAt(0)
  }
  
  // 如果是英文，取首字母并转大写
  return props.name.charAt(0).toUpperCase()
})

const fontSize = computed(() => {
  return Math.floor(props.size * 0.4)
})
</script>

<style scoped>
.default-avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
  font-weight: 600;
  user-select: none;
  flex-shrink: 0;
}
</style>