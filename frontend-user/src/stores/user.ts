import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getUserInfo, type UserInfo } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  const userInfo = ref<Partial<UserInfo>>({})
  const currentOrder = ref(null)
  const token = ref(localStorage.getItem('token') || '')

  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  const setUserInfo = (info: Partial<UserInfo>) => {
    userInfo.value = { ...userInfo.value, ...info }
  }

  const refreshUserInfo = async () => {
    try {
      const info = await getUserInfo()
      userInfo.value = info
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }

  const setCurrentOrder = (order: any) => {
    currentOrder.value = order
  }

  const clearUserData = () => {
    userInfo.value = {}
    currentOrder.value = null
    token.value = ''
    localStorage.removeItem('token')
  }

  return {
    userInfo,
    currentOrder,
    token,
    setToken,
    setUserInfo,
    refreshUserInfo,
    setCurrentOrder,
    clearUserData
  }
})