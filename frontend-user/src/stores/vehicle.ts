import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getVehicleStatus, type Vehicle } from '@/api/vehicle'

export const useVehicleStore = defineStore('vehicle', () => {
  const vehicles = ref<Vehicle[]>([])
  const loading = ref(false)

  const updateVehicles = async () => {
    loading.value = true
    try {
      const data = await getVehicleStatus()
      vehicles.value = data
    } catch (error) {
      console.error('获取车辆状态失败:', error)
    } finally {
      loading.value = false
    }
  }

  const getVehicleById = (id: number) => {
    return vehicles.value.find(vehicle => vehicle.id === id)
  }

  const updateVehicleStatus = (id: number, status: Vehicle['status']) => {
    const vehicle = getVehicleById(id)
    if (vehicle) {
      vehicle.status = status
    }
  }

  return {
    vehicles,
    loading,
    updateVehicles,
    getVehicleById,
    updateVehicleStatus
  }
})