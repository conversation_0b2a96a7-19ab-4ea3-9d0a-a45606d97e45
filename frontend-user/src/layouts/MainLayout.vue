<template>
  <el-container class="main-layout">
    <el-header class="main-header">
      <div class="header-left">
        <h1 class="system-title" @click="goToHome">校园电动车租赁系统</h1>
        <el-button text @click="showAnnouncement">
          <el-icon><component is="bell" /></el-icon>
          最新公告
        </el-button>
      </div>
      
      <div class="header-right">
        <div class="user-info">
          <span class="username">{{ userStore.userInfo.nickname || userStore.userInfo.username }}</span>
          <span class="balance">余额: ¥{{ userStore.userInfo.balance || 0 }}</span>
        </div>
        
        <el-dropdown @command="handleCommand">
          <div class="user-avatar-area">
            <img 
              v-if="userStore.userInfo.avatarUrl" 
              :src="displayAvatarUrl" 
              class="user-avatar"
              alt="用户头像"
            />
            <DefaultAvatar
              v-else
              :name="userStore.userInfo.nickname || userStore.userInfo.username"
              :size="32"
            />
            <el-icon class="dropdown-arrow">
              <component is="arrow-down" />
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="trips">我的行程</el-dropdown-item>
              <el-dropdown-item command="wallet">我的钱包</el-dropdown-item>
              <el-dropdown-item command="coupons">我的优惠券</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>
    
    <el-main class="main-content">
      <router-view />
    </el-main>

    <!-- 公告对话框 -->
    <el-dialog
      v-model="showAnnouncementDialog"
      title="最新公告"
      width="500px"
    >
      <div v-if="latestAnnouncement">
        <h3>{{ latestAnnouncement.title }}</h3>
        <p class="announcement-time">发布时间: {{ formatTime(latestAnnouncement.publishTime) }}</p>
        <div class="announcement-content">{{ latestAnnouncement.content }}</div>
      </div>
      <div v-else>
        <p>暂无公告</p>
      </div>
    </el-dialog>
  </el-container>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import DefaultAvatar from '@/components/DefaultAvatar.vue'

const router = useRouter()
const userStore = useUserStore()

const displayAvatarUrl = computed(() => {
  const avatarUrl = userStore.userInfo.avatarUrl
  if (!avatarUrl) return ''
  
  // 如果已经是完整URL，直接返回
  if (avatarUrl.startsWith('http')) {
    return avatarUrl
  }
  
  // 如果是相对路径，添加API基础路径
  return `/api${avatarUrl}`
})

const showAnnouncementDialog = ref(false)
const latestAnnouncement = ref<{
  title: string
  publishTime: string
  content: string
} | null>(null)

const goToHome = () => {
  router.push('/')
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/user/profile')
      break
    case 'trips':
      router.push('/user/trips')
      break
    case 'wallet':
      router.push('/user/wallet')
      break
    case 'coupons':
      router.push('/user/coupons')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = () => {
  ElMessageBox.confirm('确认要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.clearUserData()
    ElMessage.success('已退出登录')
    router.push('/login')
  })
}

const showAnnouncement = async () => {
  // TODO: 获取最新公告
  // try {
  //   const announcement = await getLatestAnnouncement()
  //   latestAnnouncement.value = announcement
  // } catch (error) {
  //   console.error('获取公告失败:', error)
  // }
  showAnnouncementDialog.value = true
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

onMounted(async () => {
  // 刷新用户信息
  await userStore.refreshUserInfo()
})
</script>

<style scoped>
.main-layout {
  height: 100vh;
}

.main-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.system-title {
  margin: 0;
  color: #409eff;
  font-size: 20px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.system-title:hover {
  color: #337ecc;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 14px;
}

.username {
  font-weight: 500;
  color: #303133;
}

.balance {
  color: #409eff;
  font-weight: 600;
}

.user-avatar-area {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.user-avatar-area:hover {
  background-color: #f5f7fa;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e4e7ed;
}

.dropdown-arrow {
  font-size: 14px;
  color: #909399;
}

.main-content {
  padding: 0;
  background: #f5f5f5;
}

.announcement-time {
  color: #909399;
  font-size: 12px;
  margin-bottom: 15px;
}

.announcement-content {
  line-height: 1.6;
  color: #606266;
}
</style>