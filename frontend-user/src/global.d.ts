declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@element-plus/icons-vue' {
  import type { Component } from 'vue'
  export const Car: Component
  export const User: Component
  export const ArrowDown: Component
  export const Bell: Component
}

// NodeJS全局类型
declare namespace NodeJS {
  interface Timeout {}
}