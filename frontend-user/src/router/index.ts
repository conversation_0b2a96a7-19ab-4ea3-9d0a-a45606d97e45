import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue')
    },
    {
      path: '/',
      name: 'MainLayout',
      component: () => import('@/layouts/MainLayout.vue'),
      redirect: '/home',
      meta: { requiresAuth: true },
      children: [
        {
          path: '/home',
          name: 'Home',
          component: () => import('@/views/Home.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/user/profile',
          name: 'UserProfile',
          component: () => import('@/views/UserProfile.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/user/trips',
          name: 'UserTrips',
          component: () => import('@/views/UserTrips.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/user/wallet',
          name: 'UserWallet',
          component: () => import('@/views/UserWallet.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/user/coupons',
          name: 'UserCoupons',
          component: () => import('@/views/UserCoupons.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/fault-report',
          name: 'FaultReport',
          component: () => import('@/views/FaultReport.vue'),
          meta: { requiresAuth: true }
        }
      ]
    }
  ]
})

// 全局导航守卫
router.beforeEach((to, _from, next) => {
  const token = localStorage.getItem('token')
  
  if (to.meta.requiresAuth && !token) {
    ElMessage.warning('请先登录')
    next('/login')
  } else if (to.path === '/login' && token) {
    next('/')
  } else {
    next()
  }
})

export default router