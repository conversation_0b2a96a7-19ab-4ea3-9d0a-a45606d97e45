<template>
  <div class="user-profile">
    <el-row :gutter="20">
      <!-- 头像区域 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <h3>个人头像</h3>
          </template>
          <div class="avatar-section">
            <ImageUpload
              v-model="avatarUrl"
              upload-type="avatar"
              :width="'150px'"
              :height="'150px'"
              :default-name="userStore.userInfo.nickname || userStore.userInfo.username"
              :show-default-avatar="true"
              @success="handleAvatarSuccess"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 个人信息区域 -->
      <el-col :span="16">
        <el-card>
          <template #header>
            <h3>个人信息</h3>
          </template>
          
          <el-descriptions :column="2" border v-if="userStore.userInfo">
            <el-descriptions-item label="用户名">
              {{ userStore.userInfo.username }}
            </el-descriptions-item>
            <el-descriptions-item label="昵称">
              {{ userStore.userInfo.nickname }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
              {{ userStore.userInfo.phone }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ userStore.userInfo.email }}
            </el-descriptions-item>
            <el-descriptions-item label="余额">
              <span class="balance">¥{{ userStore.userInfo.balance || 0 }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="押金">
              <el-tag :type="(userStore.userInfo.deposit && userStore.userInfo.deposit > 0) ? 'success' : 'warning'">
                {{ (userStore.userInfo.deposit && userStore.userInfo.deposit > 0) ? `已缴纳 ¥${userStore.userInfo.deposit}` : '未缴纳' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="账户状态">
              <el-tag type="success">正常</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import ImageUpload from '@/components/ImageUpload.vue'

const userStore = useUserStore()

const avatarUrl = computed({
  get: () => userStore.userInfo.avatarUrl || '',
  set: (value: string) => {
    // 这里不直接设置，等待上传成功后刷新用户信息
  }
})

const handleAvatarSuccess = async (url: string) => {
  // 刷新用户信息
  await userStore.refreshUserInfo()
  ElMessage.success('头像更新成功')
}
</script>

<style scoped>
.user-profile {
  padding: 20px;
}

.avatar-section {
  text-align: center;
  padding: 20px;
}

.balance {
  color: #409eff;
  font-weight: bold;
  font-size: 16px;
}
</style>