<template>
  <div class="user-trips">
    <el-card>
      <template #header>
        <h2>我的行程</h2>
      </template>
      
      <el-table
        :data="trips"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="订单号" width="100" />
        <el-table-column prop="vehicleId" label="车辆ID" width="100" />
        <el-table-column prop="startTime" label="开始时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="结束时间" width="180">
          <template #default="{ row }">
            {{ row.endTime ? formatTime(row.endTime) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="startLocation" label="起点" />
        <el-table-column prop="endLocation" label="终点">
          <template #default="{ row }">
            {{ row.endLocation || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="totalCost" label="费用" width="100">
          <template #default="{ row }">
            ¥{{ row.totalCost }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getUserTrips, type Trip } from '@/api/user'

const trips = ref<Trip[]>([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'ongoing':
      return 'warning'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'ongoing':
      return '进行中'
    case 'cancelled':
      return '已取消'
    default:
      return '未知'
  }
}

const loadTrips = async () => {
  loading.value = true
  try {
    const response = await getUserTrips(currentPage.value, pageSize.value)
    trips.value = response.content
    total.value = response.totalElements
  } catch (error) {
    console.error('获取行程记录失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadTrips()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadTrips()
}

onMounted(() => {
  loadTrips()
})
</script>

<style scoped>
.user-trips {
  padding: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>