<template>
  <div class="user-coupons">
    <el-card>
      <template #header>
        <h2>我的优惠券</h2>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="未使用" name="unused">
          <div class="coupons-grid">
            <div
              v-for="coupon in unusedCoupons"
              :key="coupon.id"
              class="coupon-card"
              :class="{ 'near-expiry': isNearExpiry(coupon.expiryTime) }"
            >
              <div class="coupon-header">
                <h3>{{ coupon.couponName }}</h3>
                <div class="coupon-type">{{ getCouponTypeText(coupon.couponType) }}</div>
              </div>
              <div class="coupon-body">
                <div class="discount">
                  <span v-if="coupon.discountAmount > 0">
                    ¥{{ coupon.discountAmount }}
                  </span>
                  <span v-else>
                    {{ coupon.discountPercentage }}折
                  </span>
                </div>
                <div class="condition" v-if="coupon.minAmount > 0">
                  满¥{{ coupon.minAmount }}可用
                </div>
              </div>
              <div class="coupon-footer">
                <div class="expiry-time">
                  有效期至: {{ formatTime(coupon.expiryTime) }}
                </div>
              </div>
            </div>
          </div>
          <el-empty v-if="unusedCoupons.length === 0" description="暂无未使用的优惠券" />
        </el-tab-pane>
        
        <el-tab-pane label="已使用" name="used">
          <div class="coupons-grid">
            <div
              v-for="coupon in usedCoupons"
              :key="coupon.id"
              class="coupon-card used"
            >
              <div class="coupon-header">
                <h3>{{ coupon.couponName }}</h3>
                <div class="coupon-type">{{ getCouponTypeText(coupon.couponType) }}</div>
              </div>
              <div class="coupon-body">
                <div class="discount">
                  <span v-if="coupon.discountAmount > 0">
                    ¥{{ coupon.discountAmount }}
                  </span>
                  <span v-else>
                    {{ coupon.discountPercentage }}折
                  </span>
                </div>
                <div class="condition" v-if="coupon.minAmount > 0">
                  满¥{{ coupon.minAmount }}可用
                </div>
              </div>
              <div class="coupon-footer">
                <el-tag type="success" size="small">已使用</el-tag>
              </div>
            </div>
          </div>
          <el-empty v-if="usedCoupons.length === 0" description="暂无已使用的优惠券" />
        </el-tab-pane>
        
        <el-tab-pane label="已过期" name="expired">
          <div class="coupons-grid">
            <div
              v-for="coupon in expiredCoupons"
              :key="coupon.id"
              class="coupon-card expired"
            >
              <div class="coupon-header">
                <h3>{{ coupon.couponName }}</h3>
                <div class="coupon-type">{{ getCouponTypeText(coupon.couponType) }}</div>
              </div>
              <div class="coupon-body">
                <div class="discount">
                  <span v-if="coupon.discountAmount > 0">
                    ¥{{ coupon.discountAmount }}
                  </span>
                  <span v-else>
                    {{ coupon.discountPercentage }}折
                  </span>
                </div>
                <div class="condition" v-if="coupon.minAmount > 0">
                  满¥{{ coupon.minAmount }}可用
                </div>
              </div>
              <div class="coupon-footer">
                <el-tag type="danger" size="small">已过期</el-tag>
              </div>
            </div>
          </div>
          <el-empty v-if="expiredCoupons.length === 0" description="暂无已过期的优惠券" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getUserCoupons, type UserCoupon } from '@/api/user'

const activeTab = ref('unused')
const coupons = ref<UserCoupon[]>([])
const loading = ref(false)

const unusedCoupons = computed(() => 
  coupons.value.filter(coupon => coupon.status === 'unused')
)

const usedCoupons = computed(() => 
  coupons.value.filter(coupon => coupon.status === 'used')
)

const expiredCoupons = computed(() => 
  coupons.value.filter(coupon => coupon.status === 'expired')
)

const formatTime = (time: string) => {
  return new Date(time).toLocaleDateString()
}

const getCouponTypeText = (type: string) => {
  switch (type) {
    case 'discount':
      return '折扣券'
    case 'cash':
      return '现金券'
    default:
      return '优惠券'
  }
}

const isNearExpiry = (expiryTime: string) => {
  const expiry = new Date(expiryTime)
  const now = new Date()
  const diffDays = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  return diffDays <= 7 && diffDays > 0
}

const loadCoupons = async () => {
  loading.value = true
  try {
    coupons.value = await getUserCoupons()
  } catch (error) {
    console.error('获取优惠券失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadCoupons()
})
</script>

<style scoped>
.user-coupons {
  padding: 20px;
}

.coupons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.coupon-card {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.coupon-card.near-expiry {
  border-color: #f39c12;
  box-shadow: 0 0 10px rgba(243, 156, 18, 0.3);
}

.coupon-card.used {
  background: #95a5a6;
  opacity: 0.7;
}

.coupon-card.expired {
  background: #7f8c8d;
  opacity: 0.5;
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.coupon-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.coupon-type {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.coupon-body {
  text-align: center;
  margin-bottom: 16px;
}

.discount {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.condition {
  font-size: 14px;
  opacity: 0.9;
}

.coupon-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.expiry-time {
  opacity: 0.9;
}
</style>