<template>
  <div class="user-wallet">
    <el-row :gutter="20">
      <!-- 账户概览 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>账户概览</h3>
          </template>
          <div class="account-overview">
            <div class="balance-item">
              <div class="label">当前余额</div>
              <div class="value balance">¥{{ userStore.userInfo.balance || 0 }}</div>
            </div>
            <div class="balance-item">
              <div class="label">押金状态</div>
              <div class="value">
                <el-tag :type="depositStatus.type">
                  {{ depositStatus.text }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 快捷操作 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>快捷操作</h3>
          </template>
          <div class="quick-actions">
            <el-button
              v-if="!userStore.userInfo.deposit || userStore.userInfo.deposit <= 0"
              type="primary"
              size="large"
              :loading="depositLoading"
              @click="handlePayDeposit"
              style="width: 100%; margin-bottom: 10px;"
            >
              缴纳押金
            </el-button>
            <el-button
              v-else
              type="warning"
              size="large"
              :loading="refundLoading"
              @click="handleRefundDeposit"
              style="width: 100%; margin-bottom: 10px;"
            >
              退押金到余额
            </el-button>
            <el-button
              type="success"
              size="large"
              @click="showRechargeDialog = true"
              style="width: 100%; margin-bottom: 10px;"
            >
              余额充值
            </el-button>
            <el-button
              type="info"
              size="large"
              @click="showWithdrawDialog = true"
              style="width: 100%;"
            >
              提现
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 余额支付确认对话框 -->
    <el-dialog
      v-model="showBalancePaymentDialog"
      title="确认支付"
      width="400px"
    >
      <div class="payment-confirmation">
        <div class="payment-info">
          <div class="payment-icon">
            💰
          </div>
          <h3 style="margin: 0 0 20px 0; text-align: center;">押金缴纳确认</h3>
          
          <div class="payment-details">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="支付方式">
                <el-tag type="primary">余额支付</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="押金金额">
                <span class="amount">¥{{ depositAmount }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="当前余额">
                <span :class="{ 'insufficient': (userStore.userInfo.balance || 0) < depositAmount }">
                  ¥{{ userStore.userInfo.balance || 0 }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="支付后余额">
                <span>¥{{ Math.max(0, (userStore.userInfo.balance || 0) - depositAmount) }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="payment-notice" v-if="(userStore.userInfo.balance || 0) < depositAmount">
            <el-alert
              title="余额不足"
              type="warning"
              description="当前余额不足以支付押金，请先充值"
              show-icon
              :closable="false"
            />
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showBalancePaymentDialog = false">取消</el-button>
        <el-button
          type="primary"
          :loading="depositLoading"
          :disabled="(userStore.userInfo.balance || 0) < depositAmount"
          @click="confirmBalancePayment"
        >
          确认支付
        </el-button>
      </template>
    </el-dialog>

    <!-- 充值对话框 -->
    <el-dialog
      v-model="showRechargeDialog"
      title="余额充值"
      width="400px"
    >
      <el-form :model="rechargeForm" label-width="80px">
        <el-form-item label="充值金额">
          <el-input-number
            v-model="rechargeForm.amount"
            :min="1"
            :max="10000"
            :step="10"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item>
          <div class="quick-amounts">
            <el-tag
              v-for="amount in quickAmounts"
              :key="amount"
              :type="rechargeForm.amount === amount ? 'primary' : ''"
              style="margin-right: 8px; margin-bottom: 8px; cursor: pointer;"
              @click="rechargeForm.amount = amount"
            >
              ¥{{ amount }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRechargeDialog = false">取消</el-button>
        <el-button
          type="primary"
          :loading="rechargeLoading"
          @click="handleRecharge"
        >
          确认充值
        </el-button>
      </template>
    </el-dialog>

    <!-- 提现对话框 -->
    <el-dialog
      v-model="showWithdrawDialog"
      title="申请提现"
      width="400px"
    >
      <el-form :model="withdrawForm" :rules="withdrawRules" ref="withdrawFormRef" label-width="80px">
        <el-form-item label="提现金额" prop="amount">
          <el-input-number
            v-model="withdrawForm.amount"
            :min="0.01"
            :max="userStore.userInfo.balance || 0"
            :step="10"
            :precision="2"
            style="width: 100%"
            placeholder="请输入提现金额"
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            :value="userStore.userInfo.phone || '未绑定'"
            disabled
            style="width: 100%"
          />
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            提现将转入此手机号对应的账户
          </div>
        </el-form-item>
        <el-form-item>
          <div class="withdraw-info">
            <el-alert
              title="提现说明"
              type="info"
              :closable="false"
            >
              <p>• 提现金额将转入您绑定的手机号</p>
              <p>• 当前可提现余额：¥{{ userStore.userInfo.balance || 0 }}</p>
              <p>• 提现通常在1-3个工作日内到账</p>
            </el-alert>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showWithdrawDialog = false">取消</el-button>
        <el-button
          type="primary"
          :loading="withdrawLoading"
          @click="handleWithdraw"
        >
          确认提现
        </el-button>
      </template>
    </el-dialog>

    <!-- 支付页面iframe -->
    <el-dialog
      v-model="showPaymentDialog"
      title="支付页面"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="paymentHtml" v-html="paymentHtml"></div>
      <template #footer>
        <el-button @click="closePayment">关闭</el-button>
        <el-button type="primary" @click="checkPaymentStatus">
          我已完成支付
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { payDeposit, recharge, refundDeposit } from '@/api/payment'
import { withdraw, type WithdrawRequest } from '@/api/user'

const userStore = useUserStore()

const showRechargeDialog = ref(false)
const showWithdrawDialog = ref(false)
const showPaymentDialog = ref(false)
const showBalancePaymentDialog = ref(false)
const depositLoading = ref(false)
const rechargeLoading = ref(false)
const refundLoading = ref(false)
const withdrawLoading = ref(false)
const paymentHtml = ref('')

const withdrawFormRef = ref<FormInstance>()

const rechargeForm = ref({
  amount: 50
})

const withdrawForm = ref<WithdrawRequest>({
  amount: 0
})

const withdrawRules: FormRules = {
  amount: [
    { required: true, message: '请输入提现金额', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value <= 0) {
          callback(new Error('提现金额须大于0'))
        } else if (value > (userStore.userInfo.balance || 0)) {
          callback(new Error('提现金额不能超过当前余额'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

const depositAmount = ref(200) // 押金金额
const quickAmounts = [20, 50, 100, 200, 500]

const depositStatus = computed(() => {
  const deposit = userStore.userInfo.deposit || 0
  if (deposit > 0) {
    return {
      type: 'success',
      text: `已缴纳 ¥${deposit}`
    }
  } else {
    return {
      type: 'warning',
      text: '未缴纳'
    }
  }
})

const handleRefundDeposit = async () => {
  ElMessageBox.confirm(
    '确认要将押金退回到余额吗？退款后将无法再次租车，直到重新缴纳押金。',
    '确认退押金',
    {
      confirmButtonText: '确认退款',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    refundLoading.value = true
    try {
      await refundDeposit()
      ElMessage.success('押金已成功退回到余额')
      // 刷新用户信息
      await userStore.refreshUserInfo()
    } catch (error) {
      console.error('退押金失败:', error)
    } finally {
      refundLoading.value = false
    }
  }).catch(() => {
    // 用户取消操作
  })
}

const handlePayDeposit = async () => {
  // 显示余额支付确认对话框
  showBalancePaymentDialog.value = true
}

const confirmBalancePayment = async () => {
  depositLoading.value = true
  try {
    // 检查余额是否足够
    if ((userStore.userInfo.balance || 0) < depositAmount.value) {
      ElMessage.warning('余额不足，请先充值')
      showBalancePaymentDialog.value = false
      showRechargeDialog.value = true
      return
    }

    await payDeposit()
    ElMessage.success('押金缴纳成功')
    await userStore.refreshUserInfo()
    showBalancePaymentDialog.value = false
  } catch (error) {
    console.error('缴纳押金失败:', error)
  } finally {
    depositLoading.value = false
  }
}

const handleRecharge = async () => {
  if (!rechargeForm.value.amount || rechargeForm.value.amount <= 0) {
    ElMessage.warning('请输入有效的充值金额')
    return
  }

  rechargeLoading.value = true
  try {
    const paymentForm = await recharge({ amount: rechargeForm.value.amount })
    paymentHtml.value = paymentForm
    showRechargeDialog.value = false
    showPaymentDialog.value = true
  } catch (error) {
    console.error('充值失败:', error)
  } finally {
    rechargeLoading.value = false
  }
}

const closePayment = () => {
  showPaymentDialog.value = false
  paymentHtml.value = ''
}

const handleWithdraw = async () => {
  // 检查是否绑定手机号
  if (!userStore.userInfo.phone) {
    ElMessage.warning('请先绑定手机号才能提现')
    return
  }

  if (!withdrawFormRef.value) return
  
  try {
    const valid = await withdrawFormRef.value.validate()
    if (!valid) return
  } catch (error) {
    return
  }

  ElMessageBox.confirm(
    `确认要提现 ¥${withdrawForm.value.amount} 到绑定手机号 ${userStore.userInfo.phone} 吗？`,
    '确认提现',
    {
      confirmButtonText: '确认提现',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    withdrawLoading.value = true
    try {
      await withdraw(withdrawForm.value)
      ElMessage.success('提现申请已提交，预计1-3个工作日内到账')
      showWithdrawDialog.value = false
      withdrawForm.value = { amount: 0 }
      // 刷新用户信息
      await userStore.refreshUserInfo()
    } catch (error: any) {
      console.error('提现失败:', error)
      ElMessage.error(error.message || '提现失败，请重试')
    } finally {
      withdrawLoading.value = false
    }
  }).catch(() => {
    // 用户取消操作
  })
}

const checkPaymentStatus = async () => {
  // 刷新用户信息检查支付状态
  await userStore.refreshUserInfo()
  ElMessage.success('支付状态已更新')
  closePayment()
}
</script>

<style scoped>
.user-wallet {
  padding: 20px;
}

.account-overview {
  text-align: center;
}

.balance-item {
  margin-bottom: 20px;
}

.balance-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.value {
  font-size: 18px;
  font-weight: bold;
}

.balance {
  color: #409eff;
}

.quick-actions {
  text-align: center;
}

.quick-amounts {
  margin-top: 10px;
}

/* 支付确认对话框样式 */
.payment-confirmation {
  text-align: center;
}

.payment-info {
  padding: 20px 0;
}

.payment-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.payment-details {
  margin: 20px 0;
}

.amount {
  color: #409eff;
  font-weight: bold;
  font-size: 18px;
}

.insufficient {
  color: #f56c6c;
  font-weight: bold;
}

.payment-notice {
  margin-top: 20px;
}

.withdraw-info {
  margin-top: 10px;
}

.withdraw-info p {
  margin: 5px 0;
  font-size: 14px;
}
</style>