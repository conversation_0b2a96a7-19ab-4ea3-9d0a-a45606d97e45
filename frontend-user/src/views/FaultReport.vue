<template>
  <div class="fault-report">
    <el-card>
      <template #header>
        <h2>故障上报</h2>
      </template>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        style="max-width: 600px;"
      >
        <el-form-item label="故障车辆" prop="vehicleId">
          <el-select
            v-model="form.vehicleId"
            placeholder="请选择故障车辆"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="vehicle in vehicles"
              :key="vehicle.id"
              :label="`${vehicle.licensePlate} (ID: ${vehicle.id})`"
              :value="vehicle.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="故障类型" prop="faultType">
          <el-select
            v-model="form.faultType"
            placeholder="请选择故障类型"
            style="width: 100%"
          >
            <el-option label="无法启动" value="cannot_start" />
            <el-option label="电量异常" value="battery_issue" />
            <el-option label="刹车故障" value="brake_issue" />
            <el-option label="轮胎问题" value="tire_issue" />
            <el-option label="其他机械故障" value="mechanical_issue" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="故障描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述故障情况"
          />
        </el-form-item>
        
        <el-form-item label="联系方式" prop="contactInfo">
          <el-input
            v-model="form.contactInfo"
            placeholder="请输入您的联系方式"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            :loading="submitting"
            @click="handleSubmit"
          >
            提交故障报告
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useVehicleStore } from '@/stores/vehicle'
import request from '@/utils/request'

const vehicleStore = useVehicleStore()
const formRef = ref<FormInstance>()
const submitting = ref(false)

const form = ref({
  vehicleId: null,
  faultType: '',
  description: '',
  contactInfo: ''
})

const vehicles = ref<any[]>([])

const rules: FormRules = {
  vehicleId: [
    { required: true, message: '请选择故障车辆', trigger: 'change' }
  ],
  faultType: [
    { required: true, message: '请选择故障类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入故障描述', trigger: 'blur' },
    { min: 10, message: '故障描述至少10个字符', trigger: 'blur' }
  ],
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ]
}

const loadVehicles = async () => {
  try {
    // 获取所有车辆列表用于选择
    await vehicleStore.updateVehicles()
    vehicles.value = vehicleStore.vehicles
  } catch (error) {
    console.error('获取车辆列表失败:', error)
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        await request.post('/fault-reports', {
          vehicleId: form.value.vehicleId,
          description: `故障类型: ${getFaultTypeText(form.value.faultType)}\n故障描述: ${form.value.description}`,
          contactInfo: form.value.contactInfo
        })
        
        ElMessage.success('故障报告提交成功，我们会尽快处理')
        handleReset()
      } catch (error) {
        console.error('提交故障报告失败:', error)
      } finally {
        submitting.value = false
      }
    }
  })
}

const handleReset = () => {
  formRef.value?.resetFields()
  form.value = {
    vehicleId: null,
    faultType: '',
    description: '',
    contactInfo: ''
  }
}

const getFaultTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    cannot_start: '无法启动',
    battery_issue: '电量异常',
    brake_issue: '刹车故障',
    tire_issue: '轮胎问题',
    mechanical_issue: '其他机械故障',
    other: '其他'
  }
  return typeMap[type] || type
}

onMounted(() => {
  loadVehicles()
})
</script>

<style scoped>
.fault-report {
  padding: 20px;
}
</style>