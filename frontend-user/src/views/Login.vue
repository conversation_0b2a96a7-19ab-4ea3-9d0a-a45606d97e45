<template>
  <div class="login-container">
    <!-- 背景装饰元素 -->
    <div class="bg-decoration">
      <div class="bg-circle bg-circle-1"></div>
      <div class="bg-circle bg-circle-2"></div>
      <div class="bg-circle bg-circle-3"></div>
    </div>

    <!-- 主登录卡片 -->
    <div class="login-card">
      <!-- 品牌标识区域 -->
      <div class="brand-section">
        <div class="brand-icon">
          <svg
            width="48"
            height="48"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5H6.5C5.84 5 5.28 5.42 5.08 6.01L3 12V20C3 20.55 3.45 21 4 21H5C5.55 21 6 20.55 6 20V19H18V20C18 20.55 18.45 21 19 21H20C20.55 21 21 20.55 21 20V12L18.92 6.01ZM6.5 16C5.67 16 5 15.33 5 14.5S5.67 13 6.5 13 8 13.67 8 14.5 7.33 16 6.5 16ZM17.5 16C16.67 16 16 15.33 16 14.5S16.67 13 17.5 13 19 13.67 19 14.5 18.33 16 17.5 16ZM5 11L6.5 6.5H17.5L19 11H5Z"
              fill="currentColor"
            />
          </svg>
        </div>
        <h1 class="brand-title">校园电动车</h1>
        <p class="brand-subtitle">智慧出行，绿色校园</p>
      </div>

      <!-- 表单切换标签 -->
      <div class="tab-selector">
        <button
          class="tab-button"
          :class="{ active: activeTab === 'login' }"
          @click="activeTab = 'login'"
        >
          登录
        </button>
        <button
          class="tab-button"
          :class="{ active: activeTab === 'register' }"
          @click="activeTab = 'register'"
        >
          注册
        </button>
        <div
          class="tab-indicator"
          :class="{ 'tab-register': activeTab === 'register' }"
        ></div>
      </div>

      <!-- 登录表单 -->
      <div class="form-container" v-show="activeTab === 'login'">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
        >
          <div class="form-item">
            <div class="input-group">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
              <el-form-item prop="username">
                <el-input
                  v-model="loginForm.username"
                  placeholder="用户名"
                  size="large"
                  class="custom-input"
                />
              </el-form-item>
            </div>
          </div>

          <div class="form-item">
            <div class="input-group">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM9 6C9 4.34 10.34 3 12 3S15 4.34 15 6V8H9V6Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
              <el-form-item prop="password">
                <el-input
                  v-model="loginForm.password"
                  type="password"
                  placeholder="密码"
                  size="large"
                  show-password
                  class="custom-input"
                  @keyup.enter="handleLogin"
                />
              </el-form-item>
            </div>
          </div>

          <el-button
            type="primary"
            :loading="loginLoading"
            @click="handleLogin"
            size="large"
            class="submit-btn"
          >
            <span v-if="!loginLoading">登录</span>
            <span v-else>登录中...</span>
          </el-button>
        </el-form>
      </div>

      <!-- 注册表单 -->
      <div class="form-container" v-show="activeTab === 'register'">
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          class="register-form"
        >
          <div class="form-item">
            <div class="input-group">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
              <el-form-item prop="username">
                <el-input
                  v-model="registerForm.username"
                  placeholder="用户名"
                  size="large"
                  class="custom-input"
                />
              </el-form-item>
            </div>
          </div>

          <div class="form-item">
            <div class="input-group">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM9 6C9 4.34 10.34 3 12 3S15 4.34 15 6V8H9V6Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
              <el-form-item prop="password">
                <el-input
                  v-model="registerForm.password"
                  type="password"
                  placeholder="密码"
                  size="large"
                  show-password
                  class="custom-input"
                />
              </el-form-item>
            </div>
          </div>

          <div class="form-item">
            <div class="input-group">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
              <el-form-item prop="nickname">
                <el-input
                  v-model="registerForm.nickname"
                  placeholder="昵称"
                  size="large"
                  class="custom-input"
                />
              </el-form-item>
            </div>
          </div>

          <div class="form-item">
            <div class="input-group">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M6.62 10.79C8.06 13.62 10.38 15.94 13.21 17.38L15.41 15.18C15.69 14.9 16.08 14.82 16.43 14.93C17.55 15.3 18.75 15.5 20 15.5C20.55 15.5 21 15.95 21 16.5V20C21 20.55 20.55 21 20 21C10.61 21 3 13.39 3 4C3 3.45 3.45 3 4 3H7.5C8.05 3 8.5 3.45 8.5 4C8.5 5.25 8.7 6.45 9.07 7.57C9.18 7.92 9.1 8.31 8.82 8.59L6.62 10.79Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
              <el-form-item prop="phone">
                <el-input
                  v-model="registerForm.phone"
                  placeholder="手机号"
                  size="large"
                  class="custom-input"
                />
              </el-form-item>
            </div>
          </div>

          <el-button
            type="primary"
            :loading="registerLoading"
            @click="handleRegister"
            size="large"
            class="submit-btn"
          >
            <span v-if="!registerLoading">注册</span>
            <span v-else>注册中...</span>
          </el-button>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import {
  login,
  register,
  type LoginRequest,
  type RegisterRequest,
} from "@/api/auth";
import { useUserStore } from "@/stores/user";

const router = useRouter();
const userStore = useUserStore();

const activeTab = ref("login");
const loginLoading = ref(false);
const registerLoading = ref(false);

const loginFormRef = ref<FormInstance>();
const registerFormRef = ref<FormInstance>();

const loginForm = reactive<LoginRequest>({
  username: "",
  password: "",
});

const registerForm = reactive<RegisterRequest>({
  username: "",
  password: "",
  nickname: "",
  phone: "",
});

const loginRules: FormRules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度不能少于6位", trigger: "blur" },
  ],
};

const registerRules: FormRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      pattern: /^[a-zA-Z0-9_]{4,20}$/,
      message: "用户名只能包含字母、数字、下划线，长度4-20位",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    {
      pattern: /^.{6,20}$/,
      message: "密码长度必须在6-20位之间",
      trigger: "blur",
    },
  ],
  nickname: [{ required: true, message: "请输入昵称", trigger: "blur" }],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "手机号格式不正确", trigger: "blur" },
  ],
};

const handleLogin = async () => {
  if (!loginFormRef.value) return;

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loginLoading.value = true;
      try {
        const response = await login(loginForm);

        userStore.setToken(response.token);
        userStore.setUserInfo({
          username: response.username,
          nickname: response.nickname,
          balance: response.balance,
          deposit: response.deposit,
        });

        ElMessage.success("登录成功");
        router.push("/");
      } catch (error) {
        console.error("登录失败:", error);
      } finally {
        loginLoading.value = false;
      }
    }
  });
};

const handleRegister = async () => {
  if (!registerFormRef.value) return;

  await registerFormRef.value.validate(async (valid) => {
    if (valid) {
      registerLoading.value = true;
      try {
        await register(registerForm);
        ElMessage.success("注册成功，请登录");
        activeTab.value = "login";

        // 清空注册表单
        Object.keys(registerForm).forEach((key) => {
          registerForm[key as keyof RegisterRequest] = "";
        });
        registerFormRef.value?.resetFields();
      } catch (error) {
        console.error("注册失败:", error);
      } finally {
        registerLoading.value = false;
      }
    }
  });
};
</script>

<style scoped>
/* === 容器布局 === */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: 
    linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(37, 99, 235, 0.3) 100%),
    url('../assets/background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
  padding: 20px;
  box-sizing: border-box;
}

/* === 背景装饰 === */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.15);
  backdrop-filter: blur(20px);
  animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: -5%;
  animation-delay: 0s;
}

.bg-circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: -3%;
  animation-delay: 2s;
}

.bg-circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 15%;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* === 主卡片样式 === */
.login-card {
  width: 440px;
  max-width: calc(100vw - 32px);
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(25px);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 
    0 8px 32px rgba(59, 130, 246, 0.2),
    0 2px 16px rgba(37, 99, 235, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.18);
  position: relative;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0 16px;
}

.login-card:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 12px 40px rgba(59, 130, 246, 0.25),
    0 4px 20px rgba(37, 99, 235, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* === 品牌区域 === */
.brand-section {
  text-align: center;
  margin-bottom: 32px;
}

.brand-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #3B82F6, #2563EB);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: white;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
  transition: all 0.3s ease;
}

.brand-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 24px rgba(59, 130, 246, 0.5);
}

.brand-title {
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #3B82F6, #2563EB);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* === 标签切换器 === */
.tab-selector {
  display: flex;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 28px;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-button {
  flex: 1;
  padding: 12px 20px;
  background: none;
  border: none;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
  color: rgba(255, 255, 255, 0.7);
}

.tab-button.active {
  color: #3B82F6;
}

.tab-indicator {
  position: absolute;
  top: 4px;
  left: 4px;
  width: calc(50% - 4px);
  height: calc(100% - 8px);
  background: white;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.tab-indicator.tab-register {
  left: calc(50%);
}

/* === 表单容器 === */
.form-container {
  animation: fadeInUp 0.4s ease-out;
  width: 100%;
}

.login-form,
.register-form {
  width: 100%;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === 表单项样式 === */
.form-item {
  margin-bottom: 20px;
  width: 100%;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.input-icon {
  position: absolute;
  left: 16px;
  z-index: 10;
  color: #4a5568;
  transition: color 0.3s ease;
  pointer-events: none;
}

/* === 自定义输入框样式 === */
.form-item :deep(.el-form-item) {
  margin-bottom: 0;
  width: 100%;
}

.form-item :deep(.el-form-item__error) {
  padding-top: 4px;
  font-size: 12px;
}

.custom-input {
  width: 100%;
}

.custom-input :deep(.el-input__wrapper) {
  background: #ffffff !important;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 12px 16px 12px 48px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 48px;
  width: 100%;
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(59, 130, 246, 0.5);
  background: #ffffff !important;
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: #3B82F6;
  background: #ffffff !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-input :deep(.el-input__inner) {
  font-size: 15px;
  color: #2d3748 !important;
  font-weight: 600;
  background: transparent !important;
}

.custom-input :deep(.el-input__inner::placeholder) {
  color: #718096 !important;
  font-weight: 400 !important;
}

/* 额外的强制样式 */
.custom-input :deep(input) {
  color: #2d3748 !important;
  background: transparent !important;
}

.custom-input :deep(input:focus) {
  color: #2d3748 !important;
  background: transparent !important;
}

.custom-input :deep(input:not(:focus)) {
  color: #2d3748 !important;
  background: transparent !important;
}

.custom-input :deep(input:blur) {
  color: #2d3748 !important;
  background: transparent !important;
}

/* 强制所有状态下的文字颜色 */
.custom-input :deep(.el-input__inner),
.custom-input :deep(.el-input__inner:focus),
.custom-input :deep(.el-input__inner:not(:focus)),
.custom-input :deep(.el-input__inner:active),
.custom-input :deep(.el-input__inner:hover) {
  color: #2d3748 !important;
  background: transparent !important;
}

/* 全局强制样式 - 确保在所有情况下文字都可见 */
.login-container .custom-input input,
.login-container .custom-input .el-input__inner {
  color: #2d3748 !important;
  background: transparent !important;
  -webkit-text-fill-color: #2d3748 !important;
}

.input-group:focus-within .input-icon {
  color: #3B82F6;
}

/* === 提交按钮 === */
.submit-btn {
  width: 100%;
  height: 52px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  margin-top: 8px;
  background: linear-gradient(135deg, #3B82F6, #2563EB);
  border: none;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563EB, #1D4ED8);
}

.submit-btn:active {
  transform: translateY(0);
}

.submit-btn.is-loading {
  background: linear-gradient(135deg, #a0aec0, #cbd5e0);
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 32px 24px;
    border-radius: 20px;
  }

  .brand-title {
    font-size: 26px;
  }

  .brand-icon {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 12px;
  }

  .login-card {
    padding: 24px 20px;
    border-radius: 16px;
  }

  .brand-title {
    font-size: 24px;
  }

  .brand-icon {
    width: 56px;
    height: 56px;
  }

  .custom-input :deep(.el-input__wrapper) {
    padding: 10px 12px 10px 44px;
    min-height: 44px;
  }

  .submit-btn {
    height: 48px;
  }

  .bg-circle-1,
  .bg-circle-2,
  .bg-circle-3 {
    display: none;
  }
}

@media (max-width: 360px) {
  .login-container {
    padding: 8px;
  }

  .login-card {
    padding: 20px 16px;
    margin: 0;
  }

  .brand-title {
    font-size: 22px;
  }

  .brand-icon {
    width: 52px;
    height: 52px;
  }

  .tab-button {
    padding: 10px 16px;
    font-size: 14px;
  }

  .custom-input :deep(.el-input__wrapper) {
    padding: 8px 10px 8px 40px;
    min-height: 40px;
  }

  .input-icon {
    left: 12px;
  }

  .submit-btn {
    height: 44px;
    font-size: 15px;
  }
}

/* === 深色模式支持 === */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(45, 55, 72, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .brand-title {
    color: #f7fafc;
  }

  .brand-subtitle {
    color: #a0aec0;
  }

  .tab-selector {
    background: #2d3748;
    border: 1px solid #4a5568;
  }

  .tab-button {
    color: #a0aec0;
  }

  .tab-button.active {
    color: #667eea;
  }

  .tab-indicator {
    background: #4a5568;
  }

  .custom-input :deep(.el-input__wrapper) {
    background: #2d3748;
    border-color: #4a5568;
    color: #f7fafc !important;
  }

  .custom-input :deep(.el-input__inner) {
    color: #f7fafc !important;
  }
}

/* === 强制浅色模式（移动端优先） === */
@media (max-width: 768px) {
  .login-card {
    background: rgba(255, 255, 255, 0.98) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
  }

  .brand-title {
    color: #2d3748 !important;
    background: linear-gradient(135deg, #3B82F6, #2563EB) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
  }

  .brand-subtitle {
    color: #718096 !important;
  }

  .tab-selector {
    background: #f7fafc !important;
    border: 1px solid #e2e8f0 !important;
  }

  .tab-button {
    color: #718096 !important;
  }

  .tab-button.active {
    color: #3B82F6 !important;
  }

  .tab-indicator {
    background: white !important;
  }

  .custom-input :deep(.el-input__wrapper) {
    background: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    color: #1a202c !important;
  }

  .custom-input :deep(.el-input__wrapper:hover) {
    border-color: rgba(59, 130, 246, 0.6) !important;
    background: #ffffff !important;
    color: #1a202c !important;
  }

  .custom-input :deep(.el-input__wrapper.is-focus) {
    border-color: #3B82F6 !important;
    background: #ffffff !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    color: #1a202c !important;
  }

  .custom-input :deep(.el-input__inner) {
    color: #2d3748 !important;
    font-weight: 600 !important;
    background: transparent !important;
  }

  .custom-input :deep(.el-input__inner::placeholder) {
    color: #718096 !important;
    font-weight: 400 !important;
  }

  /* 移动端额外强制样式 */
  .custom-input :deep(input) {
    color: #2d3748 !important;
    background: transparent !important;
  }

  .custom-input :deep(input:focus) {
    color: #2d3748 !important;
    background: transparent !important;
  }

  .custom-input :deep(input:not(:focus)) {
    color: #2d3748 !important;
    background: transparent !important;
  }

  /* 移动端强制所有状态下的文字颜色 */
  .custom-input :deep(.el-input__inner),
  .custom-input :deep(.el-input__inner:focus),
  .custom-input :deep(.el-input__inner:not(:focus)),
  .custom-input :deep(.el-input__inner:active),
  .custom-input :deep(.el-input__inner:hover) {
    color: #2d3748 !important;
    background: transparent !important;
  }
}
</style>
