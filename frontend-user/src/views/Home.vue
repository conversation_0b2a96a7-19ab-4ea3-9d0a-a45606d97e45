<template>
  <div class="home-container">
    <!-- 顶部信息栏 -->
    <div class="top-info-bar">
      <div class="info-section">
        <div class="info-item">
          <span class="info-label">账户余额</span>
          <span class="info-value balance">¥{{ userStore.userInfo.balance || 0 }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">押金状态</span>
          <span class="info-value" :class="depositStatusClass">{{ depositStatusText }}</span>
        </div>
      </div>
      <div class="vehicle-stats">
        <span class="stat-item">
          <span class="stat-dot available"></span>
          可用: {{ availableCount }}
        </span>
        <span class="stat-item">
          <span class="stat-dot rented"></span>
          使用中: {{ rentedCount }}
        </span>
        <span class="stat-item">
          <span class="stat-dot faulty"></span>
          故障: {{ faultyCount }}
        </span>
      </div>
    </div>

    <!-- 地图区域 -->
    <div class="map-area">
      <VehicleMap />
      
      <!-- 快捷操作浮动按钮 -->
      <div class="floating-actions">
        <el-button 
          circle 
          type="primary" 
          size="large"
          @click="router.push('/user/wallet')"
          title="钱包管理"
        >
          💳
        </el-button>
        <el-button 
          circle 
          type="success" 
          size="large"
          @click="router.push('/user/trips')"
          title="我的行程"
        >
          📊
        </el-button>
        <el-button 
          circle 
          type="warning" 
          size="large"
          @click="router.push('/fault-report')"
          title="故障上报"
        >
          ⚠️
        </el-button>
      </div>
    </div>
    
    <!-- 当前订单状态栏 -->
    <div v-if="currentOrder" class="current-order-bar">
      <div class="order-info">
        <span>正在使用车辆: {{ currentOrder.vehicleId }}</span>
        <span>开始时间: {{ formatTime(currentOrder.startTime) }}</span>
      </div>
      <el-button type="danger" @click="showEndRentDialog = true">
        一键还车
      </el-button>
    </div>

    <!-- 还车确认对话框 -->
    <el-dialog
      v-model="showEndRentDialog"
      title="确认还车"
      width="400px"
    >
      <div class="end-rent-content">
        <p>确认要结束当前租赁吗？</p>
        <el-form v-if="availableCoupons.length > 0" label-width="80px">
          <el-form-item label="选择优惠券">
            <el-select v-model="selectedCouponId" placeholder="请选择优惠券" clearable>
              <el-option
                v-for="coupon in availableCoupons"
                :key="coupon.id"
                :label="`${coupon.couponName} - ${coupon.discountAmount > 0 ? `减${coupon.discountAmount}元` : `${coupon.discountPercentage}折`}`"
                :value="coupon.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showEndRentDialog = false">取消</el-button>
        <el-button type="primary" :loading="endRentLoading" @click="handleEndRent">
          确认还车
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import VehicleMap from '@/components/VehicleMap.vue'
import { useUserStore } from '@/stores/user'
import { useVehicleStore } from '@/stores/vehicle'
import { getUserCoupons, type UserCoupon } from '@/api/user'
import { endRent } from '@/api/rent'

const router = useRouter()
const userStore = useUserStore()
const vehicleStore = useVehicleStore()

const currentOrder = ref<{
  id: number
  vehicleId: number
  startTime: string
} | null>(null)
const showEndRentDialog = ref(false)
const endRentLoading = ref(false)
const selectedCouponId = ref<number | null>(null)
const availableCoupons = ref<UserCoupon[]>([])

// 押金状态计算属性
const depositStatusText = computed(() => {
  const deposit = userStore.userInfo.deposit || 0
  return deposit > 0 ? `已缴纳 ¥${deposit}` : '未缴纳'
})

const depositStatusClass = computed(() => {
  const deposit = userStore.userInfo.deposit || 0
  return deposit > 0 ? 'status-success' : 'status-warning'
})

// 车辆统计计算属性
const availableCount = computed(() => {
  return vehicleStore.vehicles.filter(v => v.status === 'available').length
})

const rentedCount = computed(() => {
  return vehicleStore.vehicles.filter(v => v.status === 'rented').length
})

const faultyCount = computed(() => {
  return vehicleStore.vehicles.filter(v => v.status === 'faulty').length
})

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

const loadAvailableCoupons = async () => {
  try {
    const coupons = await getUserCoupons()
    availableCoupons.value = coupons.filter(coupon => coupon.status === 'unused')
  } catch (error) {
    console.error('获取优惠券失败:', error)
  }
}

const handleEndRent = async () => {
  if (!currentOrder.value) return

  endRentLoading.value = true
  try {
    const response = await endRent({
      orderId: currentOrder.value.id,
      userCouponId: selectedCouponId.value || undefined
    })

    ElMessage.success(`还车成功，本次费用: ¥${response.totalCost}`)
    showEndRentDialog.value = false
    selectedCouponId.value = null
    currentOrder.value = null
    
    // 刷新用户信息
    await userStore.refreshUserInfo()
  } catch (error: any) {
    if (error.message?.includes('余额不足')) {
      ElMessageBox.confirm(
        '余额不足，请先充值再还车',
        '余额不足',
        {
          confirmButtonText: '立即充值',
          cancelButtonText: '取消'
        }
      ).then(() => {
        // 跳转到钱包页面
        router.push('/user/wallet')
      })
    }
    console.error('还车失败:', error)
  } finally {
    endRentLoading.value = false
  }
}

onMounted(() => {
  // 检查是否有进行中的订单
  // currentOrder.value = userStore.currentOrder
  
  if (showEndRentDialog.value) {
    loadAvailableCoupons()
  }
})
</script>

<style scoped>
.home-container {
  height: 100vh;
  position: relative;
  background: #f5f7fa;
}

/* 顶部信息栏 */
.top-info-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.9), rgba(100, 181, 246, 0.9));
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.info-section {
  display: flex;
  gap: 30px;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  opacity: 0.9;
}

.info-value {
  font-size: 16px;
  font-weight: bold;
}

.info-value.balance {
  color: #4ade80;
}

.info-value.status-success {
  color: #4ade80;
}

.info-value.status-warning {
  color: #fbbf24;
}

.vehicle-stats {
  display: flex;
  gap: 20px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.stat-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.stat-dot.available {
  background-color: #4ade80;
}

.stat-dot.rented {
  background-color: #fbbf24;
}

.stat-dot.faulty {
  background-color: #f87171;
}

/* 地图区域 */
.map-area {
  height: 100vh;
  position: relative;
  padding-top: 80px; /* 为顶部信息栏留出空间 */
}

/* 浮动操作按钮 */
.floating-actions {
  position: absolute;
  right: 20px;
  bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 200;
}

.floating-actions .el-button {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
  font-size: 18px;
  width: 56px;
  height: 56px;
  transition: all 0.3s ease;
}

.floating-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* 当前订单状态栏 */
.current-order-bar {
  position: fixed;
  top: 90px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 15px 25px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.order-info {
  display: flex;
  gap: 15px;
  font-weight: 500;
}

.current-order-bar .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: bold;
}

.current-order-bar .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 还车对话框 */
.end-rent-content p {
  margin-bottom: 15px;
  font-size: 16px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-info-bar {
    flex-direction: column;
    gap: 15px;
    padding: 12px 15px;
  }
  
  .info-section {
    gap: 20px;
  }
  
  .vehicle-stats {
    gap: 15px;
  }
  
  .map-area {
    padding-top: 120px;
  }
  
  .floating-actions {
    right: 15px;
    bottom: 15px;
  }
  
  .floating-actions .el-button {
    width: 48px;
    height: 48px;
    font-size: 16px;
  }
  
  .current-order-bar {
    left: 15px;
    right: 15px;
    transform: none;
    top: 130px;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }
  
  .order-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}
</style>