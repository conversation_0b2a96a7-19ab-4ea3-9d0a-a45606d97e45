import request from '@/utils/request'

export interface RechargeRequest {
  amount: number
}

export const payDeposit = (): Promise<string> => {
  return request.post('/payment/deposit')
}

export const recharge = (data: RechargeRequest): Promise<string> => {
  return request.post('/payment/recharge', data)
}

export const refundDeposit = (): Promise<void> => {
  return request.post('/payment/refund-deposit')
}