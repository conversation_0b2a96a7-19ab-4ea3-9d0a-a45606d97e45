import request from '@/utils/request'

export interface StartRentRequest {
  vehicleId: number
}

export interface EndRentRequest {
  orderId: number
  userCouponId?: number
}

export interface RentOrder {
  id: number
  vehicleId: number
  startTime: string
  totalCost: number
}

export const startRent = (data: StartRentRequest): Promise<RentOrder> => {
  return request.post('/rent/start', data)
}

export const endRent = (data: EndRentRequest): Promise<{ totalCost: number }> => {
  return request.post('/rent/end', data)
}