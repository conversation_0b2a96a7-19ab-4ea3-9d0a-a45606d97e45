import request from '@/utils/request'

export interface UserInfo {
  id: number
  username: string
  nickname: string
  phone: string
  email?: string
  balance: number
  deposit: number
  status: string
  role: string
  avatarUrl?: string
}

export interface Trip {
  id: number
  vehicleId: number
  startTime: string
  endTime?: string
  startLocation: string
  endLocation?: string
  totalCost: number
  status: string
}

export interface UserCoupon {
  id: number
  couponName: string
  couponType: string
  discountAmount: number
  discountPercentage: number
  minAmount: number
  status: string
  expiryTime: string
}

export interface WithdrawRequest {
  amount: number
}

export const getUserInfo = (): Promise<UserInfo> => {
  return request.get('/user/info')
}

export const getUserTrips = (page: number = 1, size: number = 10): Promise<{
  content: Trip[]
  totalElements: number
  totalPages: number
}> => {
  return request.get('/user/trips', { params: { page: page - 1, size } })
}

export const getUserCoupons = (): Promise<UserCoupon[]> => {
  return request.get('/user/coupons')
}

export const withdraw = (data: WithdrawRequest): Promise<string> => {
  return request.post('/user/withdraw', data)
}