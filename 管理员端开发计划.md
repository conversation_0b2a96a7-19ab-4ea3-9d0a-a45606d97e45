### **校园电动车租赁管理系统 - PC管理员端开发计划**

#### **第一部分：项目初始化与基础架构 (KISS & YAGNI)**

1.  **技术栈确认**:
    *   与用户端保持一致，遵循 **DRY** 原则，降低学习和维护成本。
    *   **核心框架**: Vue 3 (Composition API)
    *   **构建工具**: Vite
    *   **UI库**: Element Plus
    *   **状态管理**: Pinia
    *   **路由**: Vue Router 4
    *   **HTTP请求**: Axios

2.  **初始化步骤**:
    *   在项目根目录下，使用 `npm create vite@latest frontend-admin -- --template vue-ts` 初始化Vite + TypeScript项目。
    *   安装核心依赖: `npm install element-plus pinia vue-router axios`。
    *   配置 `vite.config.ts`，设置API代理，指向后端服务 `http://localhost:8080`，解决开发环境跨域问题。

3.  **目录结构规划 (SRP)**:
    *   采用与用户端类似的结构，保持项目规范统一。
    ```
    frontend-admin/
    ├── src/
    │   ├── api/             # API请求模块 (严格按后端Controller划分)
    │   ├── components/      # 可复用组件 (如统计卡片, 地图组件)
    │   ├── layouts/         # 后台主布局 (含侧边栏导航)
    │   ├── router/          # 路由配置
    │   ├── stores/          # Pinia状态管理
    │   ├── views/           # 页面级组件 (按模块划分)
    │   └── main.ts          # 应用入口
    └── ...
    ```

4.  **核心模块封装**:
    *   **`utils/request.js`**: 封装Axios，请求拦截器统一注入管理员的JWT Token，响应拦截器处理401（Token失效，跳转登录页）、403（无权限提示）、500等通用错误。
    *   **`router/index.js`**: 配置后台所有页面路由，并设置全局导航守卫。检查Token和用户角色（`role: 'ADMIN'`），非管理员禁止访问。
    *   **`layouts/AdminLayout.vue`**: 创建后台主布局，包含固定的顶部栏、可折叠的侧边栏菜单和主内容区 `<router-view>`。

#### **第二部分：分阶段功能开发 (迭代式推进)**

##### **Phase 1: 认证与仪表盘**

*   **目标**: 管理员能够登录，并查看系统核心数据概览和车辆实时地图。
*   **页面 (`views`)**:
    *   `Login.vue`: 登录页。调用 `POST /api/auth/login`，成功后存储Token和管理员信息，跳转到仪表盘。
    *   `Dashboard.vue`: 仪表盘/首页。
        *   **核心数据统计**: 展示关键指标（KPIs），如：用户总数、车辆总数、当前租赁订单数、故障车辆数、今日收入等。（**注意**: 部分数据需要后端提供新的聚合查询接口）。
        *   **车辆实时地图**:
            *   复用或改造用户端的 `VehicleMap.vue` 组件。
            *   **定时轮询**: 每5秒调用 `GET /api/vehicles/status` 接口，获取所有车辆的实时位置和状态，并更新到Pinia store中。
            *   **投放车辆**: 地图上增加一个“投放新车”的按钮或交互。点击地图任意位置，弹出对话框，让管理员输入电量，然后调用 `POST /api/admin/vehicles` 接口，在指定坐标创建新车。

##### **Phase 2: 核心资源管理 (SRP & OCP)**

*   **目标**: 实现对车辆、用户、订单的核心管理功能。
*   **页面 (`views`)**:
    *   **车辆管理 (`VehicleManagement.vue`)**:
        *   使用 `ElTable` 展示车辆列表，数据来源 `GET /api/admin/vehicles` (分页)。
        *   功能：查看车辆详情、状态、电量、最后维护时间。
    *   **用户管理 (`UserManagement.vue`)**:
        *   使用 `ElTable` 展示用户列表。（**需后端提供 `GET /api/admin/users` 接口**）。
        *   功能：查看用户详情（余额、押金）、冻结/解冻用户（**需后端提供 `PUT /api/admin/users/{id}/status` 接口**）。
    *   **订单管理 (`OrderManagement.vue`)**:
        *   使用 `ElTable` 展示所有租赁订单，支持筛选和分页。（**需后端提供 `GET /api/admin/orders` 接口**）。
        *   功能：查看订单详情，包括用户、车辆、时间、费用等。

##### **Phase 3: 运营与财务管理**

*   **目标**: 管理计费规则、优惠券和系统公告。
*   **页面 (`views`)**:
    *   **计费规则 (`BillingRule.vue`)**:
        *   表单展示当前生效的计费规则，数据来源 `GET /api/admin/billing-rule`。
        *   提供“编辑”功能，允许管理员修改并保存规则，调用 `PUT /api/admin/billing-rule`。
    *   **优惠券管理 (`CouponManagement.vue`)**:
        *   **模板管理**: `ElTable` 展示优惠券模板列表 (`GET /api/admin/coupons`)，支持增删改查 (`POST`, `PUT`, `DELETE`)。
        *   **发放优惠券**: 提供一个表单/对话框，输入用户ID和选择优惠券模板，调用 `POST /api/admin/coupons/issue` 或 `POST /api/admin/coupons/issue/batch` 来发放。
    *   **公告管理 (`AnnouncementManagement.vue`)**:
        *   完整的CRUD功能。使用 `ElTable` 和 `ElDialog` 实现公告的增删改查，对应调用 `/api/admin/announcements` 的各个接口。

##### **Phase 4: 系统维护与监控**

*   **目标**: 处理日常运维任务，如故障处理和日志审计。
*   **页面 (`views`)**:
    *   **故障处理 (`FaultReport.vue`)**:
        *   `ElTable` 展示所有故障上报记录，数据来源 `GET /api/admin/fault-reports`。
        *   支持按状态（待处理、处理中、已解决）筛选。
        *   管理员可点击“处理”或“解决”按钮，更新报告状态，调用 `PUT /api/admin/fault-reports/{id}/status`。
    *   **操作日志 (`OperationLog.vue`)**:
        *   `ElTable` 展示管理员操作日志，数据来源 `GET /api/admin/operation-logs`。
        *   提供按操作类型或操作人搜索的功能。

#### **第三部分：后端依赖与协同**

为确保管理员端顺利开发，后端需要补充以下接口：

1.  **`GET /api/admin/dashboard/stats`**: 用于仪表盘的数据聚合接口，一次性返回核心KPI数据。
2.  **`GET /api/admin/users`**: 分页查询所有用户列表。
3.  **`PUT /api/admin/users/{id}/status`**: 更新用户状态（如冻结/解冻）。
4.  **`GET /api/admin/orders`**: 分页查询所有订单列表。
5.  **`GET /api/admin/transactions`**: 分页查询所有交易流水。

这些接口应遵循现有的API设计风格，并确保有正确的管理员权限（`hasRole('ADMIN')`）校验。
