import { defineStore } from 'pinia'
import { ref } from 'vue'
import { authApi, type LoginRequest, type LoginResponse } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('admin_token'))
  
  // 安全的用户数据初始化
  let userData: LoginResponse['userInfo'] | null = null
  try {
    const adminUserData = localStorage.getItem('admin_user')
    if (adminUserData && adminUserData !== 'null' && adminUserData !== 'undefined') {
      userData = JSON.parse(adminUserData)
    }
  } catch (error) {
    console.warn('解析用户数据失败，清除localStorage数据', error)
    localStorage.removeItem('admin_user')
    localStorage.removeItem('admin_token')
  }
  const user = ref<LoginResponse['userInfo'] | null>(userData)

  const login = async (loginData: LoginRequest) => {
    const response = await authApi.login(loginData)
    token.value = response.token
    user.value = response.userInfo
    
    localStorage.setItem('admin_token', response.token)
    localStorage.setItem('admin_user', JSON.stringify(response.userInfo))
    
    return response
  }

  const logout = async () => {
    try {
      await authApi.logout()
    } finally {
      token.value = null
      user.value = null
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    }
  }

  const isAuthenticated = () => {
    return !!token.value && !!user.value && user.value.role === 'ADMIN'
  }

  return {
    token,
    user,
    login,
    logout,
    isAuthenticated
  }
})