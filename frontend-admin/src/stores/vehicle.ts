import { defineStore } from 'pinia'
import { ref } from 'vue'
import { vehicleApi, type VehicleStatusResponse } from '@/api/vehicle'

export const useVehicleStore = defineStore('vehicle', () => {
  const vehicles = ref<VehicleStatusResponse[]>([])
  const loading = ref(false)

  const fetchVehicleStatus = async () => {
    loading.value = true
    try {
      vehicles.value = await vehicleApi.getVehicleStatus()
      console.log('获取到的车辆数据:', vehicles.value)
    } catch (error) {
      console.error('获取车辆状态失败:', error)
      vehicles.value = []
    } finally {
      loading.value = false
    }
  }

  const addVehicle = async (positionX: number, positionY: number, battery: number) => {
    await vehicleApi.addVehicle({ positionX, positionY, battery })
    await fetchVehicleStatus()
  }

  return {
    vehicles,
    loading,
    fetchVehicleStatus,
    addVehicle
  }
})