import request from '@/utils/request'

export interface Vehicle {
  id: number
  vehicleSn: string
  battery: number
  positionX: number
  positionY: number
  status: 'AVAILABLE' | 'RENTED' | 'FAULTY'
  lastMaintainTime?: string
  createTime: string
  updateTime: string
}

export interface AddVehicleRequest {
  positionX: number
  positionY: number
  battery: number
}

export interface VehicleStatusResponse {
  id: number
  vehicleSn: string
  battery: number
  positionX: number
  positionY: number
  status: string
}

export const vehicleApi = {
  getVehicles: (page: number = 1, size: number = 10): Promise<{
    list: Vehicle[]
    total: number
  }> => {
    return request.get('/admin/vehicles', { params: { page, size } })
  },
  
  getVehicleStatus: (): Promise<VehicleStatusResponse[]> => {
    return request.get('/vehicles/status')
  },
  
  addVehicle: (data: AddVehicleRequest): Promise<void> => {
    return request.post('/admin/vehicles', data)
  },
  
  updateVehicleStatus: (id: number, status: string): Promise<void> => {
    return request.put(`/admin/vehicles/${id}/status`, { status })
  }
}