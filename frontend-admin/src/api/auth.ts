import request from '@/utils/request'

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  token: string
  userInfo: {
    id: number
    username: string
    nickname: string
    phone: string
    avatarUrl: string | null
    balance: number
    deposit: number
    status: string | null
    role: string
  }
}

export const authApi = {
  login: (data: LoginRequest): Promise<LoginResponse> => {
    return request.post('/auth/login', data)
  },
  
  logout: (): Promise<void> => {
    return request.post('/auth/logout')
  }
}