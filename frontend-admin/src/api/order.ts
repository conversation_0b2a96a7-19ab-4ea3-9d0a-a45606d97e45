import request from '@/utils/request'

export interface Order {
  id: number
  userId: number
  vehicleId: number
  username: string
  startTime: string
  endTime?: string
  startLongitude: number
  startLatitude: number
  endLongitude?: number
  endLatitude?: number
  duration?: number
  amount?: number
  status: 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  createTime: string
  updateTime: string
}

export const orderApi = {
  getOrders: (page: number = 1, size: number = 10): Promise<{
    list: Order[]
    total: number
  }> => {
    return request.get('/admin/orders', { params: { page, size } })
  }
}