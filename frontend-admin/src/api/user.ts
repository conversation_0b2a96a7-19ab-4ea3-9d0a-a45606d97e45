import request from '@/utils/request'

export interface User {
  id: number
  username: string
  phone: string
  balance: number
  deposit: number
  status: 'ACTIVE' | 'FROZEN'
  createTime: string
  updateTime: string
}

export const userApi = {
  getUsers: (page: number = 1, size: number = 10): Promise<{
    list: User[]
    total: number
  }> => {
    return request.get('/admin/users', { params: { page, size } })
  },
  
  updateUserStatus: (id: number, status: 'ACTIVE' | 'FROZEN'): Promise<void> => {
    return request.put(`/admin/users/${id}/status`, { status })
  }
}