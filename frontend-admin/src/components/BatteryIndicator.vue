<template>
  <div class="battery-indicator" :class="batteryClass">
    <svg 
      width="24" 
      height="12" 
      viewBox="0 0 24 12" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      class="battery-icon"
    >
      <!-- 电池外壳 -->
      <rect
        x="1"
        y="2"
        width="20"
        height="8"
        rx="1"
        fill="none"
        stroke="currentColor"
        stroke-width="1"
      />
      
      <!-- 电池正极头 -->
      <rect
        x="21"
        y="4"
        width="2"
        height="4"
        rx="1"
        fill="currentColor"
      />
      
      <!-- 电池电量填充 -->
      <rect
        x="2"
        y="3"
        :width="batteryFillWidth"
        height="6"
        rx="0.5"
        :fill="batteryColor"
        class="battery-fill"
      />
      
      <!-- 低电量闪电警告 -->
      <g v-if="percentage <= 20" class="warning-icon">
        <path
          d="M10 1 L12 1 L11 6 L13 6 L9 11 L7 11 L8 6 L6 6 Z"
          fill="#ff4444"
          stroke="#ffffff"
          stroke-width="0.5"
        />
      </g>
    </svg>
    
    <!-- 电量百分比文字 -->
    <span class="battery-text">{{ percentage }}%</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  percentage: number
}

const props = defineProps<Props>()

// 电池填充宽度计算
const batteryFillWidth = computed(() => {
  return Math.max(0, Math.min(18, (props.percentage / 100) * 18))
})

// 电池颜色计算
const batteryColor = computed(() => {
  if (props.percentage >= 60) return '#4ade80' // 绿色
  if (props.percentage >= 30) return '#fbbf24' // 黄色
  if (props.percentage >= 20) return '#fb923c' // 橙色
  return '#ef4444' // 红色
})

// 电池样式类
const batteryClass = computed(() => ({
  'battery-low': props.percentage <= 20,
  'battery-medium': props.percentage > 20 && props.percentage <= 60,
  'battery-high': props.percentage > 60
}))
</script>

<style scoped>
.battery-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.95);
  padding: 3px 6px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  margin-top: 4px;
}

.battery-icon {
  flex-shrink: 0;
}

.battery-text {
  font-size: 9px;
  font-weight: bold;
  color: #374151;
  line-height: 1;
  min-width: 20px;
  text-align: center;
}

.battery-fill {
  transition: all 0.3s ease;
}

/* 低电量闪烁动画 */
.battery-low {
  animation: batteryBlink 1.5s infinite;
}

.battery-low .warning-icon {
  animation: warningPulse 1s infinite;
}

@keyframes batteryBlink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.7; }
}

@keyframes warningPulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

/* 电池状态颜色 */
.battery-high {
  border-color: rgba(74, 222, 128, 0.3);
}

.battery-medium {
  border-color: rgba(251, 191, 36, 0.3);
}

.battery-low {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(255, 243, 243, 0.95);
}
</style>