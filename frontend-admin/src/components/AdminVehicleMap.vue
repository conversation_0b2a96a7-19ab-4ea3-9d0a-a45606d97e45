<template>
  <div class="admin-vehicle-map-container">
    <div class="map-background" ref="mapRef" @click="handleMapClick">
      <!-- 车辆图标 -->
      <div
        v-for="vehicle in vehicleStore.vehicles"
        :key="vehicle.id"
        class="vehicle-icon"
        :class="[
          `status-${getVehicleStatus(vehicle)}`,
          { 'low-battery': getVehicleBattery(vehicle) < 20 }
        ]"
        :style="{
          left: `${getVehiclePositionX(vehicle)}%`,
          top: `${getVehiclePositionY(vehicle)}%`
        }"
        @click.stop="showVehicleInfo(vehicle)"
      >
        <ElectricCarIcon 
          :size="32"
          :body-color="getVehicleColor(getVehicleStatus(vehicle))"
          :show-electric-symbol="getVehicleBattery(vehicle) > 20"
        />
        <BatteryIndicator :percentage="getVehicleBattery(vehicle)" />
      </div>
    </div>

    <!-- 地图控制栏 -->
    <div class="map-controls">
      <el-button-group>
        <el-button 
          :type="isAddingMode ? 'primary' : 'default'"
          @click="toggleAddingMode"
          :icon="isAddingMode ? 'Check' : 'Plus'"
        >
          {{ isAddingMode ? '完成投放' : '投放车辆' }}
        </el-button>
        <el-button @click="refreshVehicles" :loading="vehicleStore.loading" icon="Refresh">
          刷新
        </el-button>
      </el-button-group>
      <div class="map-info">
        <el-text size="small" type="info">
          车辆总数: {{ vehicleStore.vehicles.length }}
        </el-text>
      </div>
    </div>

    <!-- 图例 -->
    <div class="map-legend">
      <div class="legend-item">
        <div class="legend-icon status-AVAILABLE"></div>
        <span>可用 ({{ getStatusCount('AVAILABLE') }})</span>
      </div>
      <div class="legend-item">
        <div class="legend-icon status-RENTED"></div>
        <span>租用中 ({{ getStatusCount('RENTED') }})</span>
      </div>
      <div class="legend-item">
        <div class="legend-icon status-FAULTY"></div>
        <span>故障 ({{ getStatusCount('FAULTY') }})</span>
      </div>
    </div>

    <!-- 车辆信息弹窗 -->
    <el-dialog
      v-model="showVehicleDialog"
      :title="`车辆信息 - ${getVehicleSn(selectedVehicle)}`"
      width="450px"
    >
      <div v-if="selectedVehicle" class="vehicle-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="车辆ID">
            {{ selectedVehicle.id }}
          </el-descriptions-item>
          <el-descriptions-item label="车辆编号" v-if="getVehicleSn(selectedVehicle) !== `车辆${selectedVehicle.id}`">
            {{ getVehicleSn(selectedVehicle) }}
          </el-descriptions-item>
          <el-descriptions-item label="电量">
            <el-progress
              :percentage="getVehicleBattery(selectedVehicle)"
              :color="getBatteryColor(getVehicleBattery(selectedVehicle))"
            />
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(getVehicleStatus(selectedVehicle))">
              {{ getStatusText(getVehicleStatus(selectedVehicle)) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="位置坐标">
            X: {{ getVehiclePositionX(selectedVehicle).toFixed(1) }}%, Y: {{ getVehiclePositionY(selectedVehicle).toFixed(1) }}%
          </el-descriptions-item>
          <el-descriptions-item label="最后维护时间" v-if="(selectedVehicle as any).lastMaintenanceTime">
            {{ formatDate((selectedVehicle as any).lastMaintenanceTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate((selectedVehicle as any).createTime) }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="vehicle-actions">
          <el-button-group style="width: 100%; margin-top: 20px;">
            <el-button 
              v-if="selectedVehicle.status === 'AVAILABLE'"
              type="warning" 
              @click="updateVehicleStatus('MAINTENANCE')"
            >
              设为维护
            </el-button>
            <el-button 
              v-if="selectedVehicle.status === 'MAINTENANCE'"
              type="success" 
              @click="updateVehicleStatus('AVAILABLE')"
            >
              恢复可用
            </el-button>
            <el-button 
              v-if="selectedVehicle.status !== 'FAULT'"
              type="danger" 
              @click="updateVehicleStatus('FAULT')"
            >
              标记故障
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-dialog>

    <!-- 投放车辆弹窗 -->
    <el-dialog
      v-model="showAddVehicleDialog"
      title="投放新车辆"
      width="400px"
    >
      <el-form :model="newVehicleForm" label-width="80px">
        <el-form-item label="位置X(%)">
          <el-input v-model="newVehicleForm.positionX" disabled />
        </el-form-item>
        <el-form-item label="位置Y(%)">
          <el-input v-model="newVehicleForm.positionY" disabled />
        </el-form-item>
        <el-form-item label="初始电量">
          <el-slider
            v-model="newVehicleForm.battery"
            :min="20"
            :max="100"
            show-input
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddVehicleDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAddVehicle" :loading="addingVehicle">
          确认投放
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useVehicleStore } from '@/stores/vehicle'
import { vehicleApi, type VehicleStatusResponse } from '@/api/vehicle'
import ElectricCarIcon from './ElectricCarIcon.vue'
import BatteryIndicator from './BatteryIndicator.vue'

const vehicleStore = useVehicleStore()

const mapRef = ref()
const showVehicleDialog = ref(false)
const selectedVehicle = ref<VehicleStatusResponse | null>(null)
const showAddVehicleDialog = ref(false)
const isAddingMode = ref(false)
const addingVehicle = ref(false)
let updateTimer: number | null = null

const newVehicleForm = ref({
  positionX: 0,
  positionY: 0,
  battery: 80
})

const showVehicleInfo = (vehicle: VehicleStatusResponse) => {
  selectedVehicle.value = vehicle
  showVehicleDialog.value = true
}

const getVehicleColor = (status: string) => {
  switch (status) {
    case 'AVAILABLE': return '#67c23a'
    case 'RENTED': return '#e6a23c'
    case 'FAULTY': return '#f56c6c'
    default: return '#909399'
  }
}

const getBatteryColor = (battery: number) => {
  if (battery >= 60) return '#67c23a'
  if (battery >= 30) return '#e6a23c'
  return '#f56c6c'
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'AVAILABLE': return 'success'
    case 'RENTED': return 'warning'
    case 'FAULTY': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'AVAILABLE': return '可用'
    case 'RENTED': return '租用中'
    case 'FAULTY': return '故障'
    default: return '未知'
  }
}

const getStatusCount = (status: string) => {
  return vehicleStore.vehicles.filter((v: any) => v.status === status).length
}

// 获取车辆电量 - 使用后端标准字段
const getVehicleBattery = (vehicle: any) => {
  return vehicle.battery ?? 0
}

// 获取车辆状态 - 使用后端标准状态
const getVehicleStatus = (vehicle: any) => {
  return vehicle.status || 'AVAILABLE'
}

// 获取车辆编号 - 使用后端标准字段
const getVehicleSn = (vehicle: any) => {
  return vehicle.vehicleSn || `车辆${vehicle.id}`
}

// 获取车辆坐标 - 使用后端标准百分比坐标
const getVehiclePositionX = (vehicle: any) => {
  return vehicle.positionX ?? 50
}

const getVehiclePositionY = (vehicle: any) => {
  return vehicle.positionY ?? 50
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

const toggleAddingMode = () => {
  isAddingMode.value = !isAddingMode.value
  if (isAddingMode.value) {
    ElMessage.info('请点击地图选择投放位置')
  }
}

const handleMapClick = (event: MouseEvent) => {
  if (!isAddingMode.value) return
  
  const rect = mapRef.value.getBoundingClientRect()
  const x = (event.clientX - rect.left) / rect.width
  const y = (event.clientY - rect.top) / rect.height
  
  // 转换为百分比坐标，与用户端地图保持一致
  newVehicleForm.value.positionX = parseFloat((x * 100).toFixed(2))
  newVehicleForm.value.positionY = parseFloat((y * 100).toFixed(2))
  
  showAddVehicleDialog.value = true
}

const confirmAddVehicle = async () => {
  addingVehicle.value = true
  try {
    await vehicleStore.addVehicle(
      newVehicleForm.value.positionX,
      newVehicleForm.value.positionY,
      newVehicleForm.value.battery
    )
    ElMessage.success('车辆投放成功')
    showAddVehicleDialog.value = false
    isAddingMode.value = false
  } catch (error) {
    console.error('投放车辆失败:', error)
  } finally {
    addingVehicle.value = false
  }
}

const updateVehicleStatus = async (status: string) => {
  if (!selectedVehicle.value) return
  
  try {
    await vehicleApi.updateVehicleStatus(selectedVehicle.value.id, status)
    ElMessage.success('车辆状态更新成功')
    selectedVehicle.value.status = status
    showVehicleDialog.value = false
    await vehicleStore.fetchVehicleStatus()
  } catch (error) {
    console.error('更新车辆状态失败:', error)
  }
}

const refreshVehicles = async () => {
  await vehicleStore.fetchVehicleStatus()
}

const startVehicleUpdates = () => {
  vehicleStore.fetchVehicleStatus()
  updateTimer = window.setInterval(() => {
    vehicleStore.fetchVehicleStatus()
  }, 5000)
}

const stopVehicleUpdates = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

onMounted(() => {
  startVehicleUpdates()
})

onUnmounted(() => {
  stopVehicleUpdates()
})
</script>

<style scoped>
.admin-vehicle-map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.map-background {
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(90deg, rgba(200, 200, 200, 0.3) 1px, transparent 1px),
    linear-gradient(rgba(200, 200, 200, 0.3) 1px, transparent 1px),
    linear-gradient(90deg, rgba(64, 158, 255, 0.2) 2px, transparent 2px),
    linear-gradient(rgba(64, 158, 255, 0.2) 2px, transparent 2px),
    linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 50%, #e8f5e8 100%);
  background-size: 
    40px 40px,
    40px 40px,
    200px 200px,
    200px 200px,
    100% 100%;
  background-position: 
    0 0,
    0 0,
    100px 100px,
    100px 100px,
    0 0;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.05);
  cursor: crosshair;
}

.vehicle-icon {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(5px);
}

.vehicle-icon:hover {
  transform: translate(-50%, -50%) scale(1.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
  background: rgba(255, 255, 255, 0.95);
}

.vehicle-icon.status-AVAILABLE {
  border: 2px solid rgba(103, 194, 58, 0.3);
}

.vehicle-icon.status-RENTED {
  border: 2px solid rgba(230, 162, 60, 0.3);
}

.vehicle-icon.status-FAULTY {
  border: 2px solid rgba(245, 108, 108, 0.3);
  animation: pulse 2s infinite;
}

.vehicle-icon.low-battery {
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.6; }
}

@keyframes pulse {
  0% { box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3); }
  50% { box-shadow: 0 4px 16px rgba(245, 108, 108, 0.6); }
  100% { box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3); }
}

.map-controls {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
}

.map-info {
  margin-top: 8px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  backdrop-filter: blur(5px);
}

.map-legend {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}

.legend-icon.status-AVAILABLE {
  background-color: #67c23a;
}

.legend-icon.status-RENTED {
  background-color: #e6a23c;
}

.legend-icon.status-FAULTY {
  background-color: #f56c6c;
}

.vehicle-info {
  text-align: center;
}

.vehicle-actions {
  margin-top: 20px;
}
</style>