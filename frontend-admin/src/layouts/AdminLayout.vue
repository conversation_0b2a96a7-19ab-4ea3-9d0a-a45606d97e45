<template>
  <el-container class="admin-layout">
    <el-aside width="250px" class="sidebar">
      <div class="logo">
        <h3>管理控制台</h3>
      </div>
      
      <el-menu
        :default-active="$route.path"
        router
        class="sidebar-menu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409eff"
      >
        <el-menu-item index="/">
          <el-icon><Odometer /></el-icon>
          <span>仪表盘</span>
        </el-menu-item>
        
        <el-sub-menu index="2">
          <template #title>
            <el-icon><Management /></el-icon>
            <span>资源管理</span>
          </template>
          <el-menu-item index="/vehicles">
            <el-icon><Bicycle /></el-icon>
            <span>车辆管理</span>
          </el-menu-item>
          <el-menu-item index="/users">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
          <el-menu-item index="/orders">
            <el-icon><Document /></el-icon>
            <span>订单管理</span>
          </el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="3">
          <template #title>
            <el-icon><Setting /></el-icon>
            <span>运营管理</span>
          </template>
          <el-menu-item index="/billing">
            <el-icon><Money /></el-icon>
            <span>计费规则</span>
          </el-menu-item>
          <el-menu-item index="/coupons">
            <el-icon><Ticket /></el-icon>
            <span>优惠券管理</span>
          </el-menu-item>
          <el-menu-item index="/announcements">
            <el-icon><Bell /></el-icon>
            <span>公告管理</span>
          </el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="4">
          <template #title>
            <el-icon><Monitor /></el-icon>
            <span>系统维护</span>
          </template>
          <el-menu-item index="/faults">
            <el-icon><Warning /></el-icon>
            <span>故障处理</span>
          </el-menu-item>
          <el-menu-item index="/logs">
            <el-icon><Document /></el-icon>
            <span>操作日志</span>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-aside>
    
    <el-container>
      <el-header class="header">
        <div class="header-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>{{ breadcrumbTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-icon><Avatar /></el-icon>
              {{ authStore.user?.username }}
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Odometer,
  Management,
  Bicycle,
  User,
  Document,
  Setting,
  Money,
  Ticket,
  Bell,
  Monitor,
  Warning,
  Avatar,
  ArrowDown
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const breadcrumbTitle = computed(() => {
  const titles: Record<string, string> = {
    '/': '仪表盘',
    '/vehicles': '车辆管理',
    '/users': '用户管理', 
    '/orders': '订单管理',
    '/billing': '计费规则',
    '/coupons': '优惠券管理',
    '/announcements': '公告管理',
    '/faults': '故障处理',
    '/logs': '操作日志'
  }
  return titles[route.path] || '管理控制台'
})

const handleCommand = async (command: string) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确认退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      await authStore.logout()
      ElMessage.success('已退出登录')
      router.push('/login')
    } catch (error) {
      console.log('取消退出')
    }
  }
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  overflow: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bfcbd9;
  font-size: 18px;
  border-bottom: 1px solid #434a5a;
}

.logo h3 {
  margin: 0;
}

.sidebar-menu {
  height: calc(100vh - 60px);
  border-right: none;
}

.header {
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #666;
  font-size: 14px;
}

.user-info .el-icon {
  margin: 0 4px;
}

.main-content {
  background: #f5f5f5;
  padding: 20px;
}
</style>