import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      component: () => import('@/layouts/AdminLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue')
        },
        {
          path: 'vehicles',
          name: 'VehicleManagement',
          component: () => import('@/views/VehicleManagement.vue')
        },
        {
          path: 'users',
          name: 'UserManagement', 
          component: () => import('@/views/UserManagement.vue')
        },
        {
          path: 'orders',
          name: 'OrderManagement',
          component: () => import('@/views/OrderManagement.vue')
        },
        {
          path: 'billing',
          name: 'BillingRule',
          component: () => import('@/views/BillingRule.vue')
        },
        {
          path: 'coupons',
          name: 'CouponManagement',
          component: () => import('@/views/CouponManagement.vue')
        },
        {
          path: 'announcements',
          name: 'AnnouncementManagement',
          component: () => import('@/views/AnnouncementManagement.vue')
        },
        {
          path: 'faults',
          name: 'FaultReport',
          component: () => import('@/views/FaultReport.vue')
        },
        {
          path: 'logs',
          name: 'OperationLog',
          component: () => import('@/views/OperationLog.vue')
        }
      ]
    }
  ]
})

router.beforeEach(async (to) => {
  const authStore = useAuthStore()
  
  // 调试：输出路由守卫状态
  console.log('路由守卫检查:', {
    to: to.name,
    requiresAuth: to.meta.requiresAuth,
    token: authStore.token,
    user: authStore.user,
    isAuthenticated: authStore.isAuthenticated()
  })
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated()) {
    console.log('需要认证但未登录，跳转到登录页')
    return { name: 'Login' }
  }
  
  if (to.name === 'Login' && authStore.isAuthenticated()) {
    console.log('已登录用户访问登录页，跳转到仪表板')
    return { name: 'Dashboard' }
  }
})

export default router