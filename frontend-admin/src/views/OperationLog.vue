<template>
  <div class="operation-log">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>操作日志</span>
          <el-button type="primary" @click="refreshList" :loading="loading" icon="Refresh">
            刷新
          </el-button>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true">
          <el-form-item label="操作类型">
            <el-select v-model="filterType" placeholder="全部类型" clearable>
              <el-option label="用户管理" value="USER_MANAGEMENT" />
              <el-option label="车辆管理" value="VEHICLE_MANAGEMENT" />
              <el-option label="订单管理" value="ORDER_MANAGEMENT" />
              <el-option label="系统配置" value="SYSTEM_CONFIG" />
            </el-select>
          </el-form-item>
          <el-form-item label="操作人">
            <el-input v-model="filterOperator" placeholder="输入操作人" clearable />
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="operationLogs" :loading="loading" stripe>
        <el-table-column prop="id" label="日志ID" width="80" />
        <el-table-column prop="operator" label="操作人" width="100" />
        <el-table-column prop="operation" label="操作类型" width="120" />
        <el-table-column prop="description" label="操作描述" show-overflow-tooltip />
        <el-table-column prop="ip" label="IP地址" width="130" />
        <el-table-column prop="createTime" label="操作时间" width="180" />
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetails(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)
const filterType = ref('')
const filterOperator = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(100)

const operationLogs = ref([
  {
    id: 1,
    operator: 'admin',
    operation: '用户管理',
    description: '冻结用户账户 user123',
    ip: '*************',
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    operator: 'admin',
    operation: '车辆管理',
    description: '更新车辆状态为维护中',
    ip: '*************',
    createTime: '2024-01-01 11:30:00'
  },
  {
    id: 3,
    operator: 'admin',
    operation: '系统配置',
    description: '更新计费规则',
    ip: '*************',
    createTime: '2024-01-01 14:20:00'
  }
])

const refreshList = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('刷新成功')
  }, 500)
}

const viewDetails = (log: any) => {
  ElMessage.info(`查看操作详情: ${log.description}`)
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>