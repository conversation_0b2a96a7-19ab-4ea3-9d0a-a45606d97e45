<template>
  <div class="user-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshList" :loading="loading" icon="Refresh">
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="用户状态">
            <el-select v-model="filterForm.status" placeholder="全部状态" clearable @change="handleFilter">
              <el-option label="正常" value="ACTIVE" />
              <el-option label="冻结" value="FROZEN" />
            </el-select>
          </el-form-item>
          <el-form-item label="搜索用户">
            <el-input
              v-model="filterForm.keyword"
              placeholder="输入用户名或手机号"
              clearable
              @input="handleFilter"
              style="width: 200px"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 用户列表表格 -->
      <el-table 
        :data="filteredUsers" 
        :loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="用户ID" width="80" />
        
        <el-table-column prop="username" label="用户名" width="150" />
        
        <el-table-column prop="phone" label="手机号" width="130" />
        
        <el-table-column label="余额" width="120">
          <template #default="{ row }">
            <span class="amount">¥{{ (row.balance / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="押金" width="120">
          <template #default="{ row }">
            <span class="amount deposit">¥{{ (row.deposit / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
              {{ row.status === 'ACTIVE' ? '正常' : '冻结' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="注册时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="最后活跃" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updateTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button 
                v-if="row.status === 'ACTIVE'"
                size="small" 
                type="warning"
                @click="updateUserStatus(row, 'FROZEN')"
              >
                冻结账户
              </el-button>
              <el-button 
                v-if="row.status === 'FROZEN'"
                size="small" 
                type="success"
                @click="updateUserStatus(row, 'ACTIVE')"
              >
                解冻账户
              </el-button>
              <el-button 
                size="small"
                @click="showUserDetails(row)"
              >
                详情
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户详情弹窗 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="`用户详情 - ${selectedUser?.username}`"
      width="700px"
    >
      <div v-if="selectedUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID" :span="1">
            {{ selectedUser.id }}
          </el-descriptions-item>
          <el-descriptions-item label="状态" :span="1">
            <el-tag :type="selectedUser.status === 'ACTIVE' ? 'success' : 'danger'">
              {{ selectedUser.status === 'ACTIVE' ? '正常' : '冻结' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户名" :span="1">
            {{ selectedUser.username }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号" :span="1">
            {{ selectedUser.phone }}
          </el-descriptions-item>
          <el-descriptions-item label="账户余额" :span="1">
            <span class="amount large">¥{{ (selectedUser.balance / 100).toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="押金" :span="1">
            <span class="amount deposit large">¥{{ (selectedUser.deposit / 100).toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="注册时间" :span="1">
            {{ formatDate(selectedUser.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后活跃时间" :span="1">
            {{ formatDate(selectedUser.updateTime) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 用户统计信息 -->
        <div class="user-stats">
          <h4>用户统计</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="总订单数" :value="0" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="总消费" :value="0" prefix="¥" :precision="2" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="会员等级" value="普通用户" />
            </el-col>
          </el-row>
        </div>
      </div>
      
      <template #footer>
        <div class="footer-actions">
          <el-button 
            v-if="selectedUser?.status === 'ACTIVE'"
            type="warning"
            @click="updateUserStatus(selectedUser, 'FROZEN')"
          >
            冻结账户
          </el-button>
          <el-button 
            v-if="selectedUser?.status === 'FROZEN'"
            type="success"
            @click="updateUserStatus(selectedUser, 'ACTIVE')"
          >
            解冻账户
          </el-button>
          <el-button @click="showDetailDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { userApi, type User } from '@/api/user'

const loading = ref(false)
const users = ref<User[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const showDetailDialog = ref(false)
const selectedUser = ref<User | null>(null)

const filterForm = ref({
  status: '',
  keyword: ''
})

// 筛选后的用户列表
const filteredUsers = computed(() => {
  let filtered = users.value

  // 状态筛选
  if (filterForm.value.status) {
    filtered = filtered.filter(u => u.status === filterForm.value.status)
  }

  // 关键词搜索
  if (filterForm.value.keyword) {
    const keyword = filterForm.value.keyword.toLowerCase()
    filtered = filtered.filter((u: any) => 
      u.username.toLowerCase().includes(keyword) ||
      u.phone.includes(keyword)
    )
  }

  return filtered
})

const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await userApi.getUsers(currentPage.value, pageSize.value)
    users.value = response.list
    total.value = response.total
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const refreshList = () => {
  fetchUsers()
}

const handleFilter = () => {
  // 筛选是在前端进行的，所以不需要重新请求数据
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchUsers()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchUsers()
}

const updateUserStatus = async (user: User, newStatus: 'ACTIVE' | 'FROZEN') => {
  const actionText = newStatus === 'FROZEN' ? '冻结' : '解冻'
  
  try {
    await ElMessageBox.confirm(
      `确认${actionText}用户 ${user.username} 的账户吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await userApi.updateUserStatus(user.id, newStatus)
    ElMessage.success(`用户账户${actionText}成功`)
    
    // 更新本地数据
    user.status = newStatus
    
    // 如果是详情弹窗中的用户，也要更新
    if (selectedUser.value && selectedUser.value.id === user.id) {
      selectedUser.value.status = newStatus
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error(`${actionText}用户账户失败:`, error)
      ElMessage.error(`${actionText}用户账户失败`)
    }
  }
}

const showUserDetails = (user: User) => {
  selectedUser.value = user
  showDetailDialog.value = true
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-management {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.amount {
  font-weight: bold;
}

.amount.deposit {
  color: #e6a23c;
}

.amount.large {
  font-size: 16px;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.user-detail {
  margin: 20px 0;
}

.user-stats {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.user-stats h4 {
  margin-bottom: 16px;
  color: #303133;
}

.footer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-button-group .el-button {
  margin-right: 0;
}

:deep(.el-statistic__content) {
  font-size: 16px;
}

:deep(.el-statistic__number) {
  color: #303133;
}
</style>