<template>
  <div class="vehicle-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>车辆管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshList" :loading="loading" icon="Refresh">
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="状态筛选">
            <el-select v-model="filterForm.status" placeholder="全部状态" clearable @change="handleFilter">
              <el-option label="可用" value="AVAILABLE" />
              <el-option label="使用中" value="IN_USE" />
              <el-option label="维护中" value="MAINTENANCE" />
              <el-option label="故障" value="FAULT" />
            </el-select>
          </el-form-item>
          <el-form-item label="电量">
            <el-select v-model="filterForm.batteryLevel" placeholder="全部电量" clearable @change="handleFilter">
              <el-option label="低电量 (<30%)" value="low" />
              <el-option label="中电量 (30-60%)" value="medium" />
              <el-option label="高电量 (>60%)" value="high" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 车辆列表表格 -->
      <el-table 
        :data="filteredVehicles" 
        :loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="车辆ID" width="80" />
        
        <el-table-column label="电量" width="120">
          <template #default="{ row }">
            <el-progress
              :percentage="row.batteryLevel"
              :color="getBatteryColor(row.batteryLevel)"
              :status="row.batteryLevel <= 20 ? 'exception' : undefined"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="位置" width="200">
          <template #default="{ row }">
            <div class="location-info">
              <div>经度: {{ row.longitude.toFixed(6) }}</div>
              <div>纬度: {{ row.latitude.toFixed(6) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="最后维护时间" width="180">
          <template #default="{ row }">
            {{ row.lastMaintenanceTime ? formatDate(row.lastMaintenanceTime) : '无记录' }}
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button 
                v-if="row.status === 'AVAILABLE'"
                size="small" 
                type="warning"
                @click="updateVehicleStatus(row, 'MAINTENANCE')"
              >
                设为维护
              </el-button>
              <el-button 
                v-if="row.status === 'MAINTENANCE'"
                size="small" 
                type="success"
                @click="updateVehicleStatus(row, 'AVAILABLE')"
              >
                恢复可用
              </el-button>
              <el-button 
                v-if="row.status !== 'FAULT'"
                size="small" 
                type="danger"
                @click="updateVehicleStatus(row, 'FAULT')"
              >
                标记故障
              </el-button>
              <el-button 
                size="small"
                @click="showVehicleDetails(row)"
              >
                详情
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 车辆详情弹窗 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="`车辆详情 - ID: ${selectedVehicle?.id}`"
      width="600px"
    >
      <div v-if="selectedVehicle" class="vehicle-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="车辆ID" :span="1">
            {{ selectedVehicle.id }}
          </el-descriptions-item>
          <el-descriptions-item label="状态" :span="1">
            <el-tag :type="getStatusType(selectedVehicle.status)">
              {{ getStatusText(selectedVehicle.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="电量" :span="2">
            <el-progress
              :percentage="selectedVehicle.batteryLevel"
              :color="getBatteryColor(selectedVehicle.batteryLevel)"
              :status="selectedVehicle.batteryLevel <= 20 ? 'exception' : undefined"
            />
          </el-descriptions-item>
          <el-descriptions-item label="经度" :span="1">
            {{ selectedVehicle.longitude.toFixed(6) }}
          </el-descriptions-item>
          <el-descriptions-item label="纬度" :span="1">
            {{ selectedVehicle.latitude.toFixed(6) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后维护时间" :span="2">
            {{ selectedVehicle.lastMaintenanceTime ? formatDate(selectedVehicle.lastMaintenanceTime) : '无维护记录' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="1">
            {{ formatDate(selectedVehicle.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" :span="1">
            {{ formatDate(selectedVehicle.updateTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { vehicleApi, type Vehicle } from '@/api/vehicle'

const loading = ref(false)
const vehicles = ref<Vehicle[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const showDetailDialog = ref(false)
const selectedVehicle = ref<Vehicle | null>(null)

const filterForm = ref({
  status: '',
  batteryLevel: ''
})

// 筛选后的车辆列表
const filteredVehicles = computed(() => {
  let filtered = vehicles.value

  // 状态筛选
  if (filterForm.value.status) {
    filtered = filtered.filter(v => v.status === filterForm.value.status)
  }

  // 电量筛选
  if (filterForm.value.batteryLevel) {
    switch (filterForm.value.batteryLevel) {
      case 'low':
        filtered = filtered.filter((v: any) => v.batteryLevel < 30)
        break
      case 'medium':
        filtered = filtered.filter((v: any) => v.batteryLevel >= 30 && v.batteryLevel <= 60)
        break
      case 'high':
        filtered = filtered.filter((v: any) => v.batteryLevel > 60)
        break
    }
  }

  return filtered
})

const fetchVehicles = async () => {
  loading.value = true
  try {
    const response = await vehicleApi.getVehicles(currentPage.value, pageSize.value)
    vehicles.value = response.list
    total.value = response.total
  } catch (error) {
    console.error('获取车辆列表失败:', error)
    ElMessage.error('获取车辆列表失败')
  } finally {
    loading.value = false
  }
}

const refreshList = () => {
  fetchVehicles()
}

const handleFilter = () => {
  // 筛选是在前端进行的，所以不需要重新请求数据
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchVehicles()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchVehicles()
}

const updateVehicleStatus = async (vehicle: Vehicle, newStatus: string) => {
  try {
    await ElMessageBox.confirm(
      `确认将车辆 ${vehicle.id} 状态更改为 ${getStatusText(newStatus)} 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await vehicleApi.updateVehicleStatus(vehicle.id, newStatus)
    ElMessage.success('车辆状态更新成功')
    
    // 更新本地数据
    vehicle.status = newStatus as Vehicle['status']
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('更新车辆状态失败:', error)
      ElMessage.error('更新车辆状态失败')
    }
  }
}

const showVehicleDetails = (vehicle: Vehicle) => {
  selectedVehicle.value = vehicle
  showDetailDialog.value = true
}

const getBatteryColor = (battery: number) => {
  if (battery >= 60) return '#67c23a'
  if (battery >= 30) return '#e6a23c'
  return '#f56c6c'
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'AVAILABLE': return 'success'
    case 'IN_USE': return 'warning'
    case 'MAINTENANCE': return 'info'
    case 'FAULT': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'AVAILABLE': return '可用'
    case 'IN_USE': return '使用中'
    case 'MAINTENANCE': return '维护中'
    case 'FAULT': return '故障'
    default: return '未知'
  }
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchVehicles()
})
</script>

<style scoped>
.vehicle-management {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.location-info {
  font-size: 12px;
  line-height: 1.4;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.vehicle-detail {
  margin: 20px 0;
}

.el-button-group .el-button {
  margin-right: 0;
}
</style>