<template>
  <div class="announcement-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>公告管理</span>
          <el-button type="primary" @click="createAnnouncement" icon="Plus">
            发布公告
          </el-button>
        </div>
      </template>

      <el-table :data="announcements" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="公告标题" />
        <el-table-column prop="content" label="公告内容" show-overflow-tooltip />
        <el-table-column prop="createTime" label="发布时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="editAnnouncement(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteAnnouncement(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const announcements = ref([
  { id: 1, title: '系统维护通知', content: '系统将于今晚进行维护，请提前做好准备', createTime: '2024-01-01 10:00:00' },
  { id: 2, title: '新功能上线', content: '新增车辆预约功能，欢迎体验', createTime: '2024-01-02 15:30:00' }
])

const createAnnouncement = () => {
  ElMessage.info('发布公告功能开发中')
}

const editAnnouncement = (announcement: any) => {
  ElMessage.info(`编辑公告: ${announcement.title}`)
}

const deleteAnnouncement = (announcement: any) => {
  ElMessage.info(`删除公告: ${announcement.title}`)
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>