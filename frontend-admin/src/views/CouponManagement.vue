<template>
  <div class="coupon-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>优惠券管理</span>
          <el-button type="primary" @click="createCoupon" icon="Plus">
            新建优惠券
          </el-button>
        </div>
      </template>

      <el-table :data="coupons" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="优惠券名称" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === 'DISCOUNT' ? 'success' : 'warning'">
              {{ row.type === 'DISCOUNT' ? '折扣券' : '代金券' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="value" label="面值" width="100" />
        <el-table-column prop="validUntil" label="有效期至" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="editCoupon(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteCoupon(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const coupons = ref([
  { id: 1, name: '新用户专享', type: 'DISCOUNT', value: '8折', validUntil: '2024-12-31' },
  { id: 2, name: '周末特惠', type: 'VOUCHER', value: '5元', validUntil: '2024-12-31' }
])

const createCoupon = () => {
  ElMessage.info('创建优惠券功能开发中')
}

const editCoupon = (coupon: any) => {
  ElMessage.info(`编辑优惠券: ${coupon.name}`)
}

const deleteCoupon = (coupon: any) => {
  ElMessage.info(`删除优惠券: ${coupon.name}`)
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>