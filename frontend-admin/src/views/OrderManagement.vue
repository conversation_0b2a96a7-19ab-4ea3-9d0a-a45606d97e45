<template>
  <div class="order-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>订单管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshList" :loading="loading" icon="Refresh">
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="订单状态">
            <el-select v-model="filterForm.status" placeholder="全部状态" clearable @change="handleFilter">
              <el-option label="进行中" value="IN_PROGRESS" />
              <el-option label="已完成" value="COMPLETED" />
              <el-option label="已取消" value="CANCELLED" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleFilter"
            />
          </el-form-item>
          <el-form-item label="搜索">
            <el-input
              v-model="filterForm.keyword"
              placeholder="用户名或订单ID"
              clearable
              @input="handleFilter"
              style="width: 200px"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 订单列表表格 -->
      <el-table 
        :data="filteredOrders" 
        :loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="订单ID" width="80" />
        
        <el-table-column prop="username" label="用户名" width="120" />
        
        <el-table-column label="车辆ID" width="80">
          <template #default="{ row }">
            {{ row.vehicleId }}
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="开始时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.startTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="结束时间" width="180">
          <template #default="{ row }">
            {{ row.endTime ? formatDate(row.endTime) : '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="租用时长" width="120">
          <template #default="{ row }">
            {{ row.duration ? formatDuration(row.duration) : '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="费用" width="100">
          <template #default="{ row }">
            <span class="amount" v-if="row.amount">
              ¥{{ (row.amount / 100).toFixed(2) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="起点位置" width="200">
          <template #default="{ row }">
            <div class="location-info">
              <div>经度: {{ row.startLongitude.toFixed(6) }}</div>
              <div>纬度: {{ row.startLatitude.toFixed(6) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="终点位置" width="200">
          <template #default="{ row }">
            <div class="location-info" v-if="row.endLongitude && row.endLatitude">
              <div>经度: {{ row.endLongitude.toFixed(6) }}</div>
              <div>纬度: {{ row.endLatitude.toFixed(6) }}</div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              @click="showOrderDetails(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情弹窗 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="`订单详情 - ID: ${selectedOrder?.id}`"
      width="800px"
    >
      <div v-if="selectedOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单ID" :span="1">
            {{ selectedOrder.id }}
          </el-descriptions-item>
          <el-descriptions-item label="状态" :span="1">
            <el-tag :type="getStatusType(selectedOrder.status)">
              {{ getStatusText(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户ID" :span="1">
            {{ selectedOrder.userId }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名" :span="1">
            {{ selectedOrder.username }}
          </el-descriptions-item>
          <el-descriptions-item label="车辆ID" :span="1">
            {{ selectedOrder.vehicleId }}
          </el-descriptions-item>
          <el-descriptions-item label="租用时长" :span="1">
            {{ selectedOrder.duration ? formatDuration(selectedOrder.duration) : '进行中' }}
          </el-descriptions-item>
          <el-descriptions-item label="费用" :span="1">
            <span class="amount large" v-if="selectedOrder.amount">
              ¥{{ (selectedOrder.amount / 100).toFixed(2) }}
            </span>
            <span v-else>计算中</span>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间" :span="1">
            {{ formatDate(selectedOrder.startTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间" :span="2">
            {{ selectedOrder.endTime ? formatDate(selectedOrder.endTime) : '未结束' }}
          </el-descriptions-item>
          <el-descriptions-item label="起点位置" :span="2">
            经度: {{ selectedOrder.startLongitude.toFixed(6) }}, 
            纬度: {{ selectedOrder.startLatitude.toFixed(6) }}
          </el-descriptions-item>
          <el-descriptions-item label="终点位置" :span="2" v-if="selectedOrder.endLongitude && selectedOrder.endLatitude">
            经度: {{ selectedOrder.endLongitude.toFixed(6) }}, 
            纬度: {{ selectedOrder.endLatitude.toFixed(6) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="1">
            {{ formatDate(selectedOrder.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" :span="1">
            {{ formatDate(selectedOrder.updateTime) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 订单轨迹图 -->
        <div class="order-map" v-if="selectedOrder.endLongitude && selectedOrder.endLatitude">
          <h4>行程轨迹</h4>
          <div class="simple-map">
            <div class="route-line">
              <div class="start-point">起点</div>
              <div class="route-path"></div>
              <div class="end-point">终点</div>
            </div>
            <div class="coordinates">
              <div>起点: {{ selectedOrder.startLongitude.toFixed(4) }}, {{ selectedOrder.startLatitude.toFixed(4) }}</div>
              <div>终点: {{ selectedOrder.endLongitude.toFixed(4) }}, {{ selectedOrder.endLatitude.toFixed(4) }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { orderApi, type Order } from '@/api/order'

const loading = ref(false)
const orders = ref<Order[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const showDetailDialog = ref(false)
const selectedOrder = ref<Order | null>(null)

const filterForm = ref({
  status: '',
  dateRange: [] as string[],
  keyword: ''
})

// 筛选后的订单列表
const filteredOrders = computed(() => {
  let filtered = orders.value

  // 状态筛选
  if (filterForm.value.status) {
    filtered = filtered.filter(o => o.status === filterForm.value.status)
  }

  // 时间范围筛选
  if (filterForm.value.dateRange && filterForm.value.dateRange.length === 2) {
    const [startDate, endDate] = filterForm.value.dateRange
    filtered = filtered.filter((o: any) => {
      const createTime = new Date(o.createTime).getTime()
      return createTime >= new Date(startDate).getTime() && 
             createTime <= new Date(endDate).getTime()
    })
  }

  // 关键词搜索
  if (filterForm.value.keyword) {
    const keyword = filterForm.value.keyword.toLowerCase()
    filtered = filtered.filter((o: any) => 
      o.username.toLowerCase().includes(keyword) ||
      o.id.toString().includes(keyword)
    )
  }

  return filtered
})

const fetchOrders = async () => {
  loading.value = true
  try {
    const response = await orderApi.getOrders(currentPage.value, pageSize.value)
    orders.value = response.list
    total.value = response.total
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

const refreshList = () => {
  fetchOrders()
}

const handleFilter = () => {
  // 筛选是在前端进行的，所以不需要重新请求数据
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchOrders()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchOrders()
}

const showOrderDetails = (order: Order) => {
  selectedOrder.value = order
  showDetailDialog.value = true
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'IN_PROGRESS': return 'warning'
    case 'COMPLETED': return 'success'
    case 'CANCELLED': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'IN_PROGRESS': return '进行中'
    case 'COMPLETED': return '已完成'
    case 'CANCELLED': return '已取消'
    default: return '未知'
  }
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  
  if (hours > 0) {
    return `${hours}小时${mins}分钟`
  } else {
    return `${mins}分钟`
  }
}

onMounted(() => {
  fetchOrders()
})
</script>

<style scoped>
.order-management {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.location-info {
  font-size: 12px;
  line-height: 1.4;
}

.amount {
  font-weight: bold;
  color: #67c23a;
}

.amount.large {
  font-size: 16px;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.order-detail {
  margin: 20px 0;
}

.order-map {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.order-map h4 {
  margin-bottom: 16px;
  color: #303133;
}

.simple-map {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.route-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.start-point, .end-point {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
}

.start-point {
  background: #67c23a;
  color: white;
}

.end-point {
  background: #e6a23c;
  color: white;
}

.route-path {
  flex: 1;
  height: 3px;
  background: linear-gradient(to right, #67c23a, #e6a23c);
  margin: 0 20px;
  border-radius: 2px;
  position: relative;
}

.route-path::after {
  content: '';
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid #e6a23c;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.coordinates {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

:deep(.el-date-editor) {
  width: 350px;
}
</style>