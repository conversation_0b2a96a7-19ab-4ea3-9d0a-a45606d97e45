<template>
  <div class="billing-rule">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>计费规则管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshRule" :loading="loading" icon="Refresh">
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div class="billing-content">
        <el-form 
          :model="billingForm" 
          :rules="billingRules" 
          ref="billingFormRef"
          label-width="120px"
          :disabled="!isEditing"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="基础费用" prop="baseFee">
                <el-input 
                  v-model="billingForm.baseFee" 
                  placeholder="输入基础费用"
                  type="number"
                  step="0.01"
                >
                  <template #append>元</template>
                </el-input>
                <div class="form-tip">每次租车的起步价</div>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="每分钟费用" prop="pricePerMinute">
                <el-input 
                  v-model="billingForm.pricePerMinute" 
                  placeholder="输入每分钟费用"
                  type="number"
                  step="0.01"
                >
                  <template #append>元/分钟</template>
                </el-input>
                <div class="form-tip">超过起步时间后按分钟计费</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="免费时长" prop="freeMinutes">
                <el-input 
                  v-model="billingForm.freeMinutes" 
                  placeholder="输入免费时长"
                  type="number"
                >
                  <template #append>分钟</template>
                </el-input>
                <div class="form-tip">基础费用包含的免费使用时长</div>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="押金金额" prop="depositAmount">
                <el-input 
                  v-model="billingForm.depositAmount" 
                  placeholder="输入押金金额"
                  type="number"
                  step="0.01"
                >
                  <template #append>元</template>
                </el-input>
                <div class="form-tip">用户租车需要缴纳的押金</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="规则说明" prop="description">
                <el-input 
                  v-model="billingForm.description" 
                  type="textarea"
                  :rows="4"
                  placeholder="输入计费规则的详细说明"
                />
                <div class="form-tip">用户可见的计费规则说明</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="生效时间" prop="effectiveTime">
            <el-date-picker
              v-model="billingForm.effectiveTime"
              type="datetime"
              placeholder="选择生效时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <div class="form-tip">新规则的生效时间，留空表示立即生效</div>
          </el-form-item>
        </el-form>

        <!-- 计费预览 -->
        <div class="billing-preview">
          <h4>计费示例</h4>
          <el-table :data="previewData" border style="width: 100%">
            <el-table-column prop="duration" label="使用时长" width="120" />
            <el-table-column prop="calculation" label="计费计算" />
            <el-table-column prop="totalFee" label="总费用" width="120">
              <template #default="{ row }">
                <span class="amount">¥{{ row.totalFee }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button-group v-if="!isEditing">
            <el-button type="primary" @click="startEdit" icon="Edit">
              编辑规则
            </el-button>
            <el-button @click="viewHistory" icon="Clock">
              历史记录
            </el-button>
          </el-button-group>
          
          <el-button-group v-else>
            <el-button type="success" @click="saveRule" :loading="saving" icon="Check">
              保存
            </el-button>
            <el-button @click="cancelEdit" icon="Close">
              取消
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 历史记录弹窗 -->
    <el-dialog
      v-model="showHistoryDialog"
      title="计费规则历史记录"
      width="800px"
    >
      <el-table :data="historyData" stripe>
        <el-table-column prop="version" label="版本" width="80" />
        <el-table-column label="基础费用" width="100">
          <template #default="{ row }">
            ¥{{ (row.baseFee / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="每分钟费用" width="120">
          <template #default="{ row }">
            ¥{{ (row.pricePerMinute / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="免费时长" width="100">
          <template #default="{ row }">
            {{ row.freeMinutes }}分钟
          </template>
        </el-table-column>
        <el-table-column label="押金" width="100">
          <template #default="{ row }">
            ¥{{ (row.depositAmount / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="生效时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.effectiveTime) }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      
      <template #footer>
        <el-button @click="showHistoryDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'

const loading = ref(false)
const saving = ref(false)
const isEditing = ref(false)
const showHistoryDialog = ref(false)
const billingFormRef = ref<FormInstance>()

// 当前计费规则
const billingForm = ref({
  baseFee: '2.00',
  pricePerMinute: '0.30',
  freeMinutes: '5',
  depositAmount: '199.00',
  description: '基础费用2元包含5分钟免费使用时长，超出后按每分钟0.3元计费。',
  effectiveTime: ''
})

// 原始数据备份
const originalBillingForm = ref<any>({})

// 历史记录数据
const historyData = ref([
  {
    version: 'v1.2',
    baseFee: 200,
    pricePerMinute: 30,
    freeMinutes: 5,
    depositAmount: 19900,
    effectiveTime: '2024-01-01 00:00:00',
    createTime: '2024-01-01 00:00:00'
  },
  {
    version: 'v1.1',
    baseFee: 150,
    pricePerMinute: 25,
    freeMinutes: 3,
    depositAmount: 19900,
    effectiveTime: '2023-12-01 00:00:00',
    createTime: '2023-12-01 00:00:00'
  }
])

// 表单验证规则
const billingRules: FormRules = {
  baseFee: [
    { required: true, message: '请输入基础费用', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入有效的金额', trigger: 'blur' }
  ],
  pricePerMinute: [
    { required: true, message: '请输入每分钟费用', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入有效的金额', trigger: 'blur' }
  ],
  freeMinutes: [
    { required: true, message: '请输入免费时长', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的分钟数', trigger: 'blur' }
  ],
  depositAmount: [
    { required: true, message: '请输入押金金额', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入有效的金额', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入规则说明', trigger: 'blur' },
    { min: 10, max: 500, message: '规则说明长度应在10到500字符之间', trigger: 'blur' }
  ]
}

// 计费预览数据
const previewData = computed(() => {
  const baseFee = parseFloat(billingForm.value.baseFee) || 0
  const pricePerMinute = parseFloat(billingForm.value.pricePerMinute) || 0
  const freeMinutes = parseInt(billingForm.value.freeMinutes) || 0

  const scenarios = [5, 15, 30, 60, 120]
  
  return scenarios.map(duration => {
    let totalFee = baseFee
    let calculation = `基础费用: ¥${baseFee.toFixed(2)}`
    
    if (duration > freeMinutes) {
      const chargeableMinutes = duration - freeMinutes
      const additionalFee = chargeableMinutes * pricePerMinute
      totalFee += additionalFee
      calculation += ` + 超时费用: ${chargeableMinutes}分钟 × ¥${pricePerMinute.toFixed(2)} = ¥${additionalFee.toFixed(2)}`
    } else {
      calculation += ` (包含${freeMinutes}分钟免费时长)`
    }

    return {
      duration: `${duration}分钟`,
      calculation,
      totalFee: totalFee.toFixed(2)
    }
  })
})

const refreshRule = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    ElMessage.success('规则刷新成功')
  } catch (error) {
    console.error('刷新计费规则失败:', error)
    ElMessage.error('刷新计费规则失败')
  } finally {
    loading.value = false
  }
}

const startEdit = () => {
  originalBillingForm.value = { ...billingForm.value }
  isEditing.value = true
}

const cancelEdit = () => {
  billingForm.value = { ...originalBillingForm.value }
  isEditing.value = false
}

const saveRule = async () => {
  if (!billingFormRef.value) return
  
  const valid = await billingFormRef.value.validate()
  if (!valid) return

  try {
    await ElMessageBox.confirm(
      '确认保存新的计费规则吗？新规则将根据设定的生效时间开始应用。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    saving.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('计费规则保存成功')
    isEditing.value = false
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('保存计费规则失败:', error)
      ElMessage.error('保存计费规则失败')
    }
  } finally {
    saving.value = false
  }
}

const viewHistory = () => {
  showHistoryDialog.value = true
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

onMounted(() => {
  refreshRule()
})
</script>

<style scoped>
.billing-rule {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.billing-content {
  padding: 20px 0;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.billing-preview {
  margin: 40px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.billing-preview h4 {
  margin-bottom: 16px;
  color: #303133;
}

.amount {
  font-weight: bold;
  color: #67c23a;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 600;
}

:deep(.el-input-group__append) {
  background: #f5f7fa;
  border-color: #dcdfe6;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-button-group) {
  display: inline-flex;
}
</style>