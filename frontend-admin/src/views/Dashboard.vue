<template>
  <div class="dashboard">
    <!-- 数据统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon user-icon">
                <el-icon size="32"><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalUsers }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon vehicle-icon">
                <el-icon size="32"><Bicycle /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalVehicles }}</div>
                <div class="stat-label">车辆总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon order-icon">
                <el-icon size="32"><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.currentOrders }}</div>
                <div class="stat-label">进行中订单</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon revenue-icon">
                <el-icon size="32"><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">¥{{ (stats.todayRevenue / 100).toFixed(2) }}</div>
                <div class="stat-label">今日收入</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 车辆实时地图 -->
    <div class="map-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>车辆实时分布图</span>
            <div class="header-actions">
              <el-tag v-if="stats.faultVehicles > 0" type="danger" size="small">
                {{ stats.faultVehicles }} 辆故障车辆
              </el-tag>
              <el-button 
                size="small" 
                @click="refreshData"
                :loading="loading"
                icon="Refresh"
              >
                刷新
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="map-container">
          <AdminVehicleMap />
        </div>
      </el-card>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <span>快速操作</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="6">
            <el-button type="primary" size="large" block @click="$router.push('/vehicles')">
              <el-icon><Bicycle /></el-icon>
              车辆管理
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="success" size="large" block @click="$router.push('/users')">
              <el-icon><User /></el-icon>
              用户管理
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="warning" size="large" block @click="$router.push('/orders')">
              <el-icon><Document /></el-icon>
              订单管理
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="danger" size="large" block @click="$router.push('/faults')">
              <el-icon><Warning /></el-icon>
              故障处理
            </el-button>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  User, 
  Bicycle, 
  Document, 
  Money, 
  Warning
} from '@element-plus/icons-vue'
import { dashboardApi, type DashboardStats } from '@/api/dashboard'
import AdminVehicleMap from '@/components/AdminVehicleMap.vue'

const loading = ref(false)
const stats = ref<DashboardStats>({
  totalUsers: 0,
  totalVehicles: 0,
  currentOrders: 0,
  faultVehicles: 0,
  todayRevenue: 0
})

let refreshTimer: number | null = null

const fetchDashboardStats = async () => {
  try {
    loading.value = true
    const data = await dashboardApi.getStats()
    stats.value = data
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    ElMessage.error('获取仪表盘数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  await fetchDashboardStats()
}

const startAutoRefresh = () => {
  // 立即获取一次数据
  fetchDashboardStats()
  
  // 每30秒自动刷新一次数据
  refreshTimer = window.setInterval(() => {
    fetchDashboardStats()
  }, 30000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

onMounted(() => {
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-card :deep(.el-card__body) {
  padding: 20px;
  height: 100%;
  display: flex;
  align-items: center;
}

.stat-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.vehicle-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.order-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.revenue-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.map-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.map-container {
  height: 500px;
  border-radius: 8px;
  overflow: hidden;
}

.quick-actions :deep(.el-card__body) {
  padding: 20px;
}

.quick-actions .el-button {
  height: 60px;
  font-size: 16px;
}

.quick-actions .el-button .el-icon {
  margin-right: 8px;
  font-size: 20px;
}
</style>