<template>
  <div class="fault-report">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>故障处理</span>
          <el-button type="primary" @click="refreshList" :loading="loading" icon="Refresh">
            刷新
          </el-button>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true">
          <el-form-item label="处理状态">
            <el-select v-model="filterStatus" placeholder="全部状态" clearable>
              <el-option label="待处理" value="PENDING" />
              <el-option label="处理中" value="IN_PROGRESS" />
              <el-option label="已解决" value="RESOLVED" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="faultReports" :loading="loading" stripe>
        <el-table-column prop="id" label="故障ID" width="80" />
        <el-table-column prop="vehicleId" label="车辆ID" width="80" />
        <el-table-column prop="username" label="上报用户" width="100" />
        <el-table-column prop="description" label="故障描述" show-overflow-tooltip />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="上报时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="handleFault(row)">处理</el-button>
            <el-button size="small" @click="viewDetails(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)
const filterStatus = ref('')

const faultReports = ref([
  { 
    id: 1, 
    vehicleId: 101, 
    username: 'user1', 
    description: '车辆无法启动，请检查电池', 
    status: 'PENDING',
    createTime: '2024-01-01 10:00:00'
  },
  { 
    id: 2, 
    vehicleId: 102, 
    username: 'user2', 
    description: '刹车异响，需要维修', 
    status: 'IN_PROGRESS',
    createTime: '2024-01-01 11:30:00'
  }
])

const getStatusType = (status: string) => {
  switch (status) {
    case 'PENDING': return 'warning'
    case 'IN_PROGRESS': return 'primary'
    case 'RESOLVED': return 'success'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'PENDING': return '待处理'
    case 'IN_PROGRESS': return '处理中'
    case 'RESOLVED': return '已解决'
    default: return '未知'
  }
}

const refreshList = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('刷新成功')
  }, 500)
}

const handleFault = (fault: any) => {
  ElMessage.info(`处理故障: ${fault.id}`)
}

const viewDetails = (fault: any) => {
  ElMessage.info(`查看故障详情: ${fault.id}`)
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}
</style>