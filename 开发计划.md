### **校园电动车租赁管理系统 - PC用户端开发计划**

#### **第一部分：项目初始化与基础架构**

1.  **技术栈确认**:
    *   **核心框架**: Vue 3 (Composition API)
    *   **构建工具**: Vite
    *   **UI库**: Element Plus
    *   **状态管理**: Pinia
    *   **路由**: Vue Router 4
    *   **HTTP请求**: Axios

2.  **初始化步骤**:
    *   使用 `npm create vite@latest frontend-user -- --template vue-ts` 初始化Vite + TypeScript项目。
    *   安装核心依赖: `npm install element-plus pinia vue-router axios`。
    *   配置 `vite.config.ts` 以支持路径别名（例如 `@/*` 指向 `src/*`）和后端API代理，以解决跨域问题。
        ```typescript
        // vite.config.ts (示例)
        server: {
          proxy: {
            '/api': {
              target: 'http://localhost:8080', // 后端服务地址
              changeOrigin: true,
            }
          }
        }
        ```

3.  **目录结构规划**:
    ```
    frontend-user/
    ├── src/
    │   ├── api/             # API请求模块 (按业务划分)
    │   ├── assets/          # 静态资源 (图片, CSS)
    │   ├── components/      # 全局通用组件
    │   ├── layouts/         # 主体布局组件
    │   ├── router/          # 路由配置
    │   ├── stores/          # Pinia状态管理
    │   ├── utils/           # 工具函数 (如request.js)
    │   ├── views/           # 页面级组件
    │   └── main.ts          # 应用入口
    ├── package.json
    └── vite.config.ts
    ```

4.  **核心模块封装**:
    *   **`utils/request.js`**: 封装Axios，实现请求/响应拦截器。请求拦截器统一注入JWT Token；响应拦截器处理通用错误（如401跳转登录页，500系统错误提示）。
    *   **`router/index.js`**: 配置所有页面路由，并设置全局导航守卫 (`beforeEach`)。守卫逻辑：检查本地是否存在Token，若无，则在访问受保护页面时重定向至登录页。
    *   **`stores/user.js`**: 创建用户相关的Pinia store，管理Token、用户信息（昵称、余额、押金等）。
    *   **`stores/vehicle.js`**: 创建车辆相关的Pinia store，管理地图上所有车辆的实时状态列表。

#### **第二部分：分阶段功能开发 (Phase-based Development)**

##### **Phase 1: 认证与主布局**

*   **目标**: 用户可以登录、登出，应用拥有统一的导航和布局。
*   **页面组件 (`views`)**:
    *   `Login.vue`: 实现登录/注册表单。调用 `POST /api/auth/login` 和 `POST /auth/register` 接口。成功后将Token存入Pinia和localStorage，并跳转至首页。
    *   `Home.vue`: 应用程序主页，将作为核心地图和操作的容器。
*   **布局组件 (`layouts`)**:
    *   `MainLayout.vue`: 创建包含顶部导航栏和主要内容区域 (`<router-view>`) 的基础布局。
        *   **顶部导航栏**: 应包含Logo、系统名称、最新公告链接、个人中心下拉菜单（包含“我的行程”、“我的钱包”、“退出登录”）。
        *   **用户信息展示**: 在导航栏右上角，动态显示用户昵称和余额，数据从Pinia的`user` store获取。
*   **路由 (`router`)**:
    *   配置 `/login`, `/` (首页), `/user/trips`, `/user/wallet` 等核心路由。
    *   为需要登录的路由（除`/login`外）在`meta`中添加 `requiresAuth: true` 标记，供导航守卫使用。

##### **Phase 2: 核心地图与车辆仿真**

*   **目标**: 在首页动态展示所有车辆的位置和状态。
*   **核心组件 (`components`)**:
    *   `VehicleMap.vue`:
        *   **背景**: 使用一张静态的校园平面图作为地图背景。
        *   **车辆渲染**:
            *   从Pinia的`vehicle` store中获取车辆列表。
            *   使用 `v-for` 遍历列表，将每个车辆渲染为一个绝对定位的图标 (`position: absolute`)。
            *   车辆图标的 `left` 和 `top` 样式属性直接绑定到车辆的 `positionX` 和 `positionY` (百分比)。
            *   根据车辆的 `status` (`available`, `rented`, `faulty`) 和 `battery`，动态改变图标的颜色和样式（例如，低电量闪烁）。
        *   **车辆信息弹窗**: 点击车辆图标时，使用Element Plus的`ElPopover`或`ElDialog`显示车辆的详细信息（编号、状态、电量）。
*   **状态管理与API调用**:
    *   在 `Home.vue` 页面的 `onMounted` 生命周期钩子中，启动一个定时器（`setInterval`）。
    *   **每隔5秒**，定时器触发调用 `GET /api/vehicles/status` 的API请求。
    *   请求成功后，将获取到的最新车辆数据列表提交给Pinia的`vehicle` store进行更新。Vue的响应式系统将自动更新地图上的车辆视图。
    *   在 `onUnmounted` 钩子中，务必清除该定时器，防止内存泄漏。

##### **Phase 3: 核心租赁流程**

*   **目标**: 用户可以完整地执行“租车”和“还车”操作。
*   **交互逻辑**:
    *   **租车**:
        *   在`VehicleMap.vue`的车辆信息弹窗中，如果车辆状态为`available`，则显示“一键租车”按钮。
        *   点击按钮，调用 `POST /api/rent/start` 接口，参数为 `{ vehicleId }`。
        *   租车成功后，后端会返回新创建的订单信息。前端可以弹窗提示“租车成功，开始计费”，并更新Pinia中该车辆的状态为`rented`。
    *   **还车**:
        *   当用户存在进行中的订单时（该状态可存入`user` store），在页面显著位置（如导航栏下方）显示一个“一键还车”的全局状态栏或按钮。
        *   点击按钮，弹窗确认，并允许用户选择可用的优惠券（从 `GET /api/user/coupons` 获取）。
        *   调用 `POST /api/rent/end` 接口，参数为 `{ orderId, userCouponId? }`。
        *   还车成功后，提示用户支付金额，并刷新用户钱包信息 (`GET /api/user/info`)。
    *   **余额不足处理**: 如果还车接口返回特定错误码（表示余额不足），则弹窗提示用户，并提供一个“立即充值”的按钮，引导至钱包页面。

##### **Phase 4: 个人中心与支付**

*   **目标**: 用户可以管理个人信息、查看历史行程、管理钱包和优惠券。
*   **页面组件 (`views`)**:
    *   `UserProfile.vue`: 个人信息页面，展示用户基本资料。
    *   `UserTrips.vue`: “我的行程”页面。
        *   调用 `GET /api/user/trips` (分页) 接口，使用Element Plus的`ElTable`和`ElPagination`组件展示历史订单列表。
    *   `UserWallet.vue`: “我的钱包”页面。
        *   显示当前余额和押金状态 (`GET /api/user/info`)。
        *   **押金缴纳**: 如果未缴纳押金，显示“缴纳押金”按钮。点击后调用 `POST /api/payment/deposit`。
        *   **余额充值**: 提供输入框和按钮，点击后调用 `POST /api/payment/recharge`。
        *   **支付处理**: 上述两个支付接口会返回HTML表单。前端需要在新窗口或iframe中打开此HTML，以引导用户完成支付宝扫码支付。
    *   `UserCoupons.vue`: “我的优惠券”页面。
        *   调用 `GET /api/user/coupons` 接口，以列表形式展示用户持有优惠券的状态（未使用、已使用、已过期）。

##### **Phase 5: 其他功能**

*   **目标**: 实现故障上报和查看公告等辅助功能。
*   **页面组件 (`views`)**:
    *   `FaultReport.vue`: 故障上报页面。提供表单让用户选择车辆、描述问题并提交。调用 `POST /api/fault-reports` 接口。
    *   **公告展示**:
        *   在 `MainLayout.vue` 中，调用 `GET /api/announcements/latest` 获取最新公告。
        *   使用Element Plus的`ElNotification`或`ElAlert`在页面顶部显示最新公告标题，点击可查看详情。

#### **第三部分：开发建议与注意事项**

*   **组件化**: 对于可复用的UI元素（如车辆图标、信息弹窗、订单卡片），应尽可能抽象为独立的Vue组件。
*   **状态管理**: 严格区分`user` store和`vehicle` store的职责，保持数据流清晰。
*   **错误处理**: 在`request.js`中做好统一的错误处理，对用户进行友好提示。
*   **代码风格**: 使用ESLint和Prettier等工具来统一代码风格，提高团队协作效率。
*   **测试**: 优先确保核心流程（登录、租车、还车、支付）的交互逻辑正确无误。
