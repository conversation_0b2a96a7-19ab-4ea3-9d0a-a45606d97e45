### **校园电动车租赁管理系统 - 终极开发蓝图 (All-in-One)**

#### **第一部分：系统总体架构与技术选型**

1.  **架构模式**:
    *   **前后端分离**: 前端(Vue3)和后端(SpringBoot)通过RESTful API进行JSON数据交换。
    *   **双前端应用**:
        *   **PC管理后台**: 面向校园管理员，用于系统管理和仿真控制。
        *   **PC用户前台**: 面向学生用户，用于租还车辆和个人账户管理。
    *   **仿真核心**: 后端通过定时任务(Scheduler)模拟车辆的移动和电量消耗。

2.  **技术栈**:
    *   **前端 (Frontend)**:
        *   **核心框架**: Vue 3 (Composition API)
        *   **构建工具**: Vite
        *   **UI库**: Element Plus
        *   **状态管理**: Pinia
        *   **路由**: Vue Router 4
        *   **HTTP请求**: Axios
    *   **后端 (Backend)**:
        *   **核心框架**: Spring Boot
        *   **数据持久化**: Spring Data JPA / MyBatis-Plus (推荐MyBatis-Plus，对复杂查询更灵活)
        *   **数据库**: MySQL 8.0
        *   **安全认证**: Spring Security + JWT (JSON Web Token)
        *   **定时任务**: Spring Task (`@Scheduled`)
        *   **支付集成**: Alipay Easy SDK (支付宝开放平台官方SDK)

#### **第二部分：数据库核心表结构设计**

1.  **用户表 (`users`)**: `id`, `username`, `password`, `nickname`, `phone`, `avatar_url`, `balance` (余额), `deposit` (押金), `status` (正常/冻结), `create_time`.
2.  **车辆表 (`vehicles`)**: `id`, `vehicle_sn`, `status` (`available`, `rented`, `faulty`), `battery`, **`position_x` (百分比X坐标), `position_y` (百分比Y坐标)**, `last_maintain_time`.
3.  **订单表 (`orders`)**: `id`, `order_sn`, `user_id`, `vehicle_id`, `status` (`renting`, `completed`), `start_time`, `end_time`, `duration`, `base_fee`, `discount`, `final_fee`, `user_coupon_id`.
4.  **计费规则表 (`billing_rules`)**: `id`, `base_duration`, `base_fee`, `extra_duration_unit`, `extra_fee_unit`, `is_active`.
5.  **优惠券模板表 (`coupons`)**: `id`, `name`, `type` (`discount`/`deduction`), `value`, `min_spend`, `valid_days`.
6.  **用户优惠券关联表 (`user_coupons`)**: `id`, `user_id`, `coupon_id`, `status` (`unused`, `used`, `expired`), `issue_time`, `expiry_time`.
7.  **交易流水表 (`transactions`)**: `id`, `user_id`, `type` (`recharge`, `rent_payment`, `deposit`), `amount`, `related_order_sn`, `create_time`.
8.  **系统公告表 (`announcements`)**: `id`, `title`, `content`, `author_id`, `create_time`, `update_time`.
9.  **操作日志表 (`operation_logs`)**: `id`, `operator_id`, `operator_name`, `operation_type`, `details`, `ip_address`, `create_time`.
10. **故障上报表 (`fault_reports`)**: `id`, `user_id`, `vehicle_id`, `fault_type`, `description`, `image_urls`, `status` (`pending`, `processing`, `resolved`), `report_time`, `resolve_time`.
11. **支付订单表 (`payment_orders`)**: `id`, `payment_order_sn`, `business_order_sn`, `user_id`, `payment_type`, `amount`, `status` (`pending`, `success`, `failed`), `alipay_trade_no`, `create_time`, `pay_time`.

#### **第三部分：核心API接口设计**

##### **模块一：认证与用户接口**
*   `POST /api/auth/login`: 登录获取Token。
*   `GET /api/user/info`: 获取当前用户信息(钱包、押金等)。
*   `GET /api/user/coupons`: 获取我的优惠券。
*   `GET /api/user/trips`: 获取我的历史订单（分页）。

##### **模块二：定位与车辆接口**
*   **GET `/api/vehicles/status`**: **(核心轮询接口)** 获取所有车辆的最新动态信息（ID, status, battery, position_x, position_y）。
    *   **说明**: 这是前后端仿真交互的命脉。前端将以固定频率（如5秒）调用此接口，以刷新地图上所有车辆的状态和位置。
*   **POST `/api/admin/vehicles`**: (管理端)在指定坐标投放新车。
    *   **Request**: `{ "position_x": 50.0, "position_y": 50.0, "battery": 100 }`
*   **GET `/api/admin/vehicles`**: (管理端)获取车辆的静态信息列表（分页）。

##### **模块三：租赁核心接口**
*   `POST /api/rent/start`: 一键租车。
    *   **Request**: `{ "vehicleId": 1 }`
*   `POST /api/rent/end`: 一键还车。
    *   **Request**: `{ "orderId": 123, "userCouponId": 1 }` (userCouponId可选)

##### **模块四：支付接口**
*   `POST /api/payment/recharge`: 用户发起充值，后端返回支付宝支付表单HTML。
*   `POST /api/payment/deposit`: 用户发起押金缴纳。
*   `POST /api/payment/alipay/notify`: **(后端内部)** 支付宝异步回调接口，**无需前端调用，需开放公网访问权限**。

##### **模块五：系统运维接口**
*   **公告管理 (Admin)**: `GET, POST, PUT, DELETE /api/admin/announcements`
*   **用户端公告**: `GET /api/announcements/latest`
*   **操作日志 (Admin)**: `GET /api/admin/operation-logs`
*   **故障上报 (User)**: `POST /api/fault-reports`
*   **故障管理 (Admin)**: `GET /api/admin/fault-reports`, `PUT /api/admin/fault-reports/{id}/status`
*   **计费规则 (Admin)**: `GET, PUT /api/admin/billing-rule`

#### **第四部分：核心业务流程详解**

##### **流程一：定位与仿真核心流程**
1.  **后端 (仿真引擎)**: Spring Boot应用启动时，一个使用`@Scheduled(fixedRate = 3000)`注解的`SimulationService`开始工作。
2.  **后端 (循环任务)**: 每隔3秒，该服务会：
    a. 查询数据库中所有`status`为`rented`的车辆。
    b. 对每一辆“租用中”的车，执行一个简单的算法来**更新其`position_x`, `position_y`和`battery`字段**。例如，坐标随机微增，电量固定减少0.1。
    c. 将这些更改批量更新回数据库。
3.  **前端 (地图渲染)**:
    a. PC端（用户或管理后台）的地图组件在加载时，会启动一个定时器。
    b. **每隔5秒，前端调用 `GET /api/vehicles/status` 接口**。
4.  **数据与视图同步**: 前端获取到最新的车辆数据后，通过Pinia更新全局状态。Vue的响应式系统检测到数据变化，自动重新计算绑定在车辆图标上的`:style`属性(`left`, `top`)，从而在屏幕上平滑地渲染出车辆移动和状态（颜色）变化的动画。
5.  **投放新车 (交互起点)**: 管理员在后台地图上点击，前端计算出**百分比坐标**，调用`POST /api/admin/vehicles`，在数据库中创建一辆具有初始位置的静态车辆。

##### **流程二：带校验和支付的完整用户旅程**
1.  **注册登录**: 用户注册并登录。
2.  **缴纳押金**:
    a. 用户在钱包页点击“缴纳押金”，调用`POST /api/payment/deposit`。
    b. 前端在新窗口打开返回的支付宝HTML，用户扫码支付。
    c. 支付宝服务器回调后端的`/notify`接口，后端验证成功后，更新用户的`deposit`字段。
3.  **租车**:
    a. 用户在地图上点击车辆，调用`POST /api/rent/start`。
    b. 后端**严格校验**：用户状态、**押金是否足够**、余额是否为负、车辆状态。
    c. 校验通过，创建`renting`状态的订单，更新车辆状态为`rented`。
4.  **骑行中**:
    a. 后端的**仿真引擎**开始自动更新该车辆的位置和电量。
    b. 前端的**轮询机制**持续从`/api/vehicles/status`获取数据，动态展示车辆移动。
5.  **还车与计费**:
    a. 用户点击“一键还车”，调用`POST /api/rent/end`，可附带`userCouponId`。
    b. 后端计算总费用（基础费+超时费-优惠券），从用户`balance`扣款，更新订单和车辆状态。
6.  **余额不足？**: 如果还车时计算出最终费用大于用户余额，后端应返回特定错误码。前端引导用户：“余额不足，请充值以完成支付。” 用户充值成功后，可再次尝试结束订单。

#### **第五部分：前后端开发任务规划**

##### **后端 (Spring Boot)**
1.  **Phase 1: 基础框架与用户认证**
    *   [ ] 搭建Spring Boot项目，配置数据库、JPA/MyBatis。
    *   [ ] 实现用户、车辆、订单等核心实体类。
    *   [ ] 集成Spring Security + JWT，完成登录认证API。
    *   [ ] 实现AOP，自动记录操作日志。
2.  **Phase 2: 定位与仿真核心**
    *   [ ] 实现`SimulationService`，使用`@Scheduled`创建仿真循环，模拟车辆移动。
    *   [ ] 实现`GET /api/vehicles/status`接口，作为仿真数据出口。
    *   [ ] 实现后台投放车辆的API。
3.  **Phase 3: 租赁与计费逻辑**
    *   [ ] 实现租车(`start`)和还车(`end`)的核心业务逻辑，包含所有校验。
    *   [ ] 实现计费规则和优惠券的计算逻辑。
4.  **Phase 4: 支付与系统运维**
    *   [ ] 集成Alipay Easy SDK，配置沙箱参数。
    *   [ ] 实现发起支付和支付宝异步回调的接口。
    *   [ ] 完成公告、故障上报等所有剩余的CRUD接口。

##### **前端 (Vue3 + Element Plus)**
1.  **Phase 1: 骨架与布局**
    *   [ ] 搭建Vite项目，集成Element Plus, Pinia, Axios。
    *   [ ] 创建**管理后台**和**用户前台**的独立主布局组件。
    *   [ ] 配置路由和全局导航守卫（登录拦截）。
2.  **Phase 2: 核心仿真地图开发**
    *   [ ] 创建可复用的`MapControl.vue`组件。
    *   [ ] 实现**静态图片+CSS绝对定位**的车辆渲染方式。
    *   [ ] 实现调用`/api/vehicles/status`的**定时轮询**机制，并与Pinia状态联动，驱动视图更新。
    *   [ ] 在管理后台地图上实现点击投放车辆的交互。
3.  **Phase 3: 用户核心流程页面**
    *   [ ] 开发登录、仪表盘、车辆中心、我的行程、个人中心页面。
    *   [ ] 实现“一键租车”和“一键还车”的按钮及交互逻辑。
    *   [ ] 开发支付流程，处理“打开新窗口加载HTML”的逻辑。
4.  **Phase 4: 系统管理页面**
    *   [ ] 开发管理后台的所有CRUD页面：用户管理、订单查看、公告管理、故障处理、计费规则设置等。
