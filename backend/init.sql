-- 校园电动车租赁管理系统数据库初始化脚本
-- MySQL 8.0+

CREATE DATABASE IF NOT EXISTS `campus_vehicle` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `campus_vehicle`;

-- 用户表
CREATE TABLE `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) NOT NULL COMMENT '昵称',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '余额',
  `deposit` decimal(10,2) DEFAULT '0.00' COMMENT '押金',
  `status` varchar(20) DEFAULT 'NORMAL' COMMENT '状态: NORMAL-正常, FROZEN-冻结',
  `role` varchar(20) DEFAULT 'USER' COMMENT '角色: USER-用户, ADMIN-管理员',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 车辆表
CREATE TABLE `vehicles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `vehicle_sn` varchar(50) NOT NULL COMMENT '车辆编号',
  `status` varchar(20) DEFAULT 'AVAILABLE' COMMENT '状态: AVAILABLE-可用, RENTED-租用中, FAULTY-故障',
  `battery` int(11) DEFAULT '100' COMMENT '电量百分比',
  `position_x` decimal(5,2) DEFAULT NULL COMMENT 'X坐标(百分比)',
  `position_y` decimal(5,2) DEFAULT NULL COMMENT 'Y坐标(百分比)',
  `last_maintain_time` datetime DEFAULT NULL COMMENT '最后维护时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_vehicle_sn` (`vehicle_sn`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆表';

-- 订单表
CREATE TABLE `orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `vehicle_id` bigint(20) NOT NULL COMMENT '车辆ID',
  `status` varchar(20) DEFAULT 'RENTING' COMMENT '状态: RENTING-租用中, COMPLETED-已完成, CANCELLED-已取消',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` int(11) DEFAULT NULL COMMENT '租用时长(分钟)',
  `base_fee` decimal(10,2) DEFAULT NULL COMMENT '基础费用',
  `discount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `final_fee` decimal(10,2) DEFAULT NULL COMMENT '最终费用',
  `user_coupon_id` bigint(20) DEFAULT NULL COMMENT '使用的优惠券ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_sn` (`order_sn`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 计费规则表
CREATE TABLE `billing_rules` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `base_duration` int(11) NOT NULL COMMENT '基础时长(分钟)',
  `base_fee` decimal(10,2) NOT NULL COMMENT '基础费用(元)',
  `extra_duration_unit` int(11) NOT NULL COMMENT '超时计费单位(分钟)',
  `extra_fee_unit` decimal(10,2) NOT NULL COMMENT '超时单价(元)',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计费规则表';

-- 支付订单表
CREATE TABLE `payment_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `payment_order_sn` varchar(50) NOT NULL COMMENT '支付订单号',
  `business_order_sn` varchar(50) DEFAULT NULL COMMENT '业务订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `payment_type` varchar(20) NOT NULL COMMENT '支付类型: RECHARGE-充值, DEPOSIT-押金',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `status` varchar(20) DEFAULT 'PENDING' COMMENT '支付状态: PENDING-待支付, SUCCESS-成功, FAILED-失败',
  `alipay_trade_no` varchar(100) DEFAULT NULL COMMENT '支付宝交易号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_order_sn` (`payment_order_sn`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';

-- 交易流水表
CREATE TABLE `transactions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '交易类型: RECHARGE-充值, RENT_PAYMENT-租车支付, DEPOSIT-押金',
  `amount` decimal(10,2) NOT NULL COMMENT '交易金额',
  `related_order_sn` varchar(50) DEFAULT NULL COMMENT '关联订单号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易流水表';

-- 优惠券模板表
CREATE TABLE `coupons` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `type` varchar(20) NOT NULL COMMENT '优惠券类型: DISCOUNT-折扣, DEDUCTION-抵扣',
  `value` decimal(10,4) NOT NULL COMMENT '优惠值',
  `min_spend` decimal(10,2) DEFAULT '0.00' COMMENT '最低消费金额',
  `valid_days` int(11) NOT NULL COMMENT '有效天数',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券模板表';

-- 用户优惠券关联表
CREATE TABLE `user_coupons` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `coupon_id` bigint(20) NOT NULL COMMENT '优惠券ID',
  `status` varchar(20) DEFAULT 'UNUSED' COMMENT '状态: UNUSED-未使用, USED-已使用, EXPIRED-已过期',
  `issue_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '发放时间',
  `expiry_time` datetime NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券关联表';

-- 系统公告表
CREATE TABLE `announcements` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `author_id` bigint(20) NOT NULL COMMENT '发布者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_author_id` (`author_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统公告表';

-- 故障上报表
CREATE TABLE `fault_reports` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `vehicle_id` bigint(20) NOT NULL COMMENT '车辆ID',
  `fault_type` varchar(50) NOT NULL COMMENT '故障类型',
  `description` text NOT NULL COMMENT '故障描述',
  `image_urls` text COMMENT '故障图片URLs',
  `status` varchar(20) DEFAULT 'PENDING' COMMENT '状态: PENDING-待处理, PROCESSING-处理中, RESOLVED-已解决',
  `report_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上报时间',
  `resolve_time` datetime DEFAULT NULL COMMENT '解决时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='故障上报表';

-- 操作日志表
CREATE TABLE `operation_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `operator_id` bigint(20) NOT NULL COMMENT '操作者ID',
  `operator_name` varchar(50) NOT NULL COMMENT '操作者名称',
  `operation_type` varchar(100) NOT NULL COMMENT '操作类型',
  `details` text COMMENT '操作详情',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_operation_type` (`operation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 插入默认计费规则
INSERT INTO `billing_rules` (`base_duration`, `base_fee`, `extra_duration_unit`, `extra_fee_unit`, `is_active`) 
VALUES (15, 2.00, 5, 1.00, 1);

-- 插入测试优惠券模板
INSERT INTO `coupons` (`name`, `type`, `value`, `min_spend`, `valid_days`) VALUES
('新用户专享优惠券', 'DEDUCTION', 1.00, 2.00, 30),
('八折优惠券', 'DISCOUNT', 0.8, 5.00, 7);

-- 插入测试管理员账号 (密码: admin123)
INSERT INTO `users` (`username`, `password`, `nickname`, `phone`, `role`, `balance`, `deposit`) 
VALUES ('admin', '$2a$10$KXwOq8O0Eb8.FvgLZ8WIa.5x/cHSE1QmWyG5G5yJzqHzl2FG2Xkm6', '系统管理员', '13800000000', 'ADMIN', 1000.00, 200.00);

-- 插入测试用户账号 (密码: user123)
INSERT INTO `users` (`username`, `password`, `nickname`, `phone`, `role`, `balance`, `deposit`) 
VALUES ('testuser', '$2a$10$V8EWy.OMXwqNlJWdqF2oaORMeP2lF6Z0LktFjqLEaQFzQEHdQSNGG', '测试用户', '13800000001', 'USER', 50.00, 200.00);

-- 插入测试车辆
INSERT INTO `vehicles` (`vehicle_sn`, `status`, `battery`, `position_x`, `position_y`, `last_maintain_time`) VALUES
('EV2024001', 'AVAILABLE', 95, 25.50, 30.75, NOW()),
('EV2024002', 'AVAILABLE', 88, 45.20, 60.40, NOW()),
('EV2024003', 'AVAILABLE', 92, 70.80, 20.30, NOW());

-- 插入测试公告
INSERT INTO `announcements` (`title`, `content`, `author_id`) VALUES
('系统上线公告', '校园电动车租赁系统正式上线！欢迎大家使用。', 1),
('使用须知', '请文明用车，爱护车辆。如遇故障请及时上报。', 1);

-- 给测试用户发放优惠券
INSERT INTO `user_coupons` (`user_id`, `coupon_id`, `expiry_time`) VALUES
(2, 1, DATE_ADD(NOW(), INTERVAL 30 DAY)),
(2, 2, DATE_ADD(NOW(), INTERVAL 7 DAY));