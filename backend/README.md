# 校园电动车租赁管理系统 - 后端项目

## 项目结构
```
backend/
├── src/main/java/com/campus/vehicle/
│   ├── VehicleRentalApplication.java    # 启动类
│   ├── config/                          # 配置类
│   │   ├── CorsConfig.java             # CORS跨域配置
│   │   ├── SecurityConfig.java         # Spring Security配置
│   │   └── JwtAuthenticationFilter.java # JWT认证过滤器
│   ├── controller/                      # 控制器层
│   │   └── HealthController.java       # 健康检查接口
│   ├── service/                        # 服务层
│   ├── mapper/                         # 数据访问层
│   ├── entity/                         # 实体类
│   ├── dto/                            # 数据传输对象
│   ├── vo/                             # 视图对象
│   │   └── Result.java                 # 统一响应对象
│   ├── utils/                          # 工具类
│   │   └── JwtUtil.java               # JWT工具类
│   ├── aspect/                         # 切面类(AOP)
│   └── exception/                      # 异常处理
│       ├── BusinessException.java      # 业务异常
│       └── GlobalExceptionHandler.java # 全局异常处理
├── src/main/resources/
│   └── application.yml                 # 配置文件
└── pom.xml                            # Maven配置
```

## 技术栈
- **框架**: Spring Boot 2.7.18 (兼容JDK8)
- **安全**: Spring Security + JWT
- **数据库**: MySQL + MyBatis-Plus
- **支付**: 支付宝SDK
- **工具**: FastJSON, Apache Commons

## 启动说明
1. 确保已安装JDK8和Maven
2. 创建MySQL数据库: `campus_vehicle`
3. 修改 `application.yml` 中的数据库连接信息
4. 运行启动类: `VehicleRentalApplication`
5. 访问健康检查接口: `http://localhost:8080/api/health`

## 已完成功能

### 🎯 核心功能模块
1. **用户认证系统**
   - 用户注册/登录 (`/api/auth/register`, `/api/auth/login`)
   - JWT Token认证
   - 用户信息查询 (`/api/user/info`)

2. **车辆管理系统**
   - 车辆状态轮询接口 (`/api/vehicles/status`) - 前端5秒轮询
   - 管理员投放车辆 (`/api/admin/vehicles`)
   - 车辆列表查询 (`/api/admin/vehicles`)
   - **枚举类型状态管理**: `VehicleStatus.AVAILABLE/RENTED/FAULTY`

3. **租赁核心功能**
   - 一键租车 (`/api/rent/start`)
   - 一键还车 (`/api/rent/end`)
   - 当前订单查询 (`/api/rent/current`)
   - 自动计费系统

4. **仿真引擎**
   - 每3秒自动更新租用中车辆的位置和电量
   - 模拟真实的车辆移动轨迹
   - 电量消耗仿真

5. **支付系统**
   - 支付宝沙箱集成 (`/api/payment/recharge`, `/api/payment/deposit`)
   - 异步回调处理 (`/api/payment/alipay/notify`)
   - 交易流水记录

6. **优惠券系统**
   - 优惠券模板管理
   - 用户优惠券查询 (`/api/user/coupons`)
   - 折扣/抵扣计算
   - 过期状态自动更新

7. **公告系统**
   - 最新公告查询 (`/api/announcements/latest`)
   - 管理员公告CRUD (`/api/admin/announcements`)

8. **故障上报功能**
   - 用户故障报告 (`/api/fault-reports`)
   - 管理员故障处理 (`/api/admin/fault-reports`)
   - 车辆状态自动更新

9. **操作日志系统**
   - AOP自动记录管理员操作
   - 操作日志查询 (`/api/admin/operation-logs`)
   - IP地址记录

### 🔧 技术实现特色
- **完整的分层架构**: Controller → Service → Mapper
- **枚举类型安全**: `VehicleStatus`枚举确保状态类型安全
- **自定义类型处理器**: 枚举与数据库字符串自动转换
- **严格的业务校验**: 押金检查、余额验证、状态校验
- **事务一致性**: 租车/还车操作保证数据一致性  
- **自动时间填充**: MyBatis-Plus自动处理创建/更新时间
- **统一异常处理**: 全局异常拦截和标准响应格式
- **异步日志记录**: 操作日志异步记录，不影响主业务

### 📊 数据库设计
- 11张完整表结构：用户、车辆、订单、计费规则、支付订单、交易流水、优惠券、用户优惠券、公告、故障上报、操作日志
- **枚举状态管理**: 车辆状态使用枚举确保数据一致性
- 完整的测试数据和账号
- 支持软删除和时间自动填充

## 快速启动

### 1. 数据库初始化
```bash
# 执行SQL脚本
mysql -u root -p < init.sql
```

### 2. 启动应用
```bash
mvn spring-boot:run
```

### 3. 测试接口
```bash
# 健康检查
curl http://localhost:8080/api/health

# 用户登录 (测试账号: admin/admin123)
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 获取车辆状态
curl http://localhost:8080/api/vehicles/status
```

## 下一步开发计划
1. 支付宝支付集成
2. 优惠券系统
3. 公告和故障上报功能
4. 操作日志和数据统计