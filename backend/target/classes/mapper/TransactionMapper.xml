<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.campus.vehicle.mapper.TransactionMapper">

    <!-- 使用JOIN查询交易列表，避免N+1问题 -->
    <select id="selectTransactionsWithUser" resultType="com.campus.vehicle.vo.AdminTransactionVO">
        SELECT 
            t.id,
            t.user_id AS userId,
            t.type,
            CASE t.type 
                WHEN 'RECHARGE' THEN '余额充值'
                WHEN 'RENT_PAYMENT' THEN '租车支付'
                WHEN 'DEPOSIT' THEN '押金缴纳'
                WHEN 'DEPOSIT_REFUND' THEN '押金退还'
                ELSE t.type
            END AS typeDesc,
            t.amount,
            t.related_order_sn AS relatedOrderSn,
            t.create_time AS createTime,
            u.username,
            u.phone AS userPhone
        FROM transactions t 
        LEFT JOIN users u ON t.user_id = u.id 
        WHERE 1=1
        <if test="type != null and type != ''">
            AND t.type = #{type}
        </if>
        <if test="keyword != null and keyword != ''">
            AND t.related_order_sn LIKE CONCAT('%', #{keyword}, '%')
        </if>
        ORDER BY t.create_time DESC 
        LIMIT #{offset}, #{size}
    </select>

</mapper>