/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/VehicleService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/FaultReportServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/enums/CouponType.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/JwtAuthenticationFilter.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/BillingRuleMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/BillingRuleServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/OperationLogService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/VehicleMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AdminOrderController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/AdminCouponService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/enums/PaymentStatus.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/FaultReportController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/dto/IssueCouponRequest.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/UserController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/CorsConfig.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/UserCouponMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/annotation/OperationLog.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/PaymentOrder.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/CouponServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/CouponService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AdminBillingRuleController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AdminDashboardController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/enums/UserCouponStatus.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/AnnouncementMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/vo/VehicleStatusResponse.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/dto/EndRentRequest.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/AdminDashboardService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/OrderStatusTypeHandler.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/VehicleStatusTypeHandler.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/PaymentController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/SecurityConfig.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/AdminOrderServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AdminTransactionController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/MyMetaObjectHandler.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/FaultReportService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/annotation/RequireAdmin.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AdminCouponController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/AlipayConfig.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/AnnouncementServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/Vehicle.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/constant/BusinessConstants.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/enums/OrderStatus.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/AdminTransactionService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/vo/UserCouponVO.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/PaymentOrderMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/dto/StartRentRequest.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/UserService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AdminOperationLogController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/BillingRule.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/AdminCouponServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AdminVehicleController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/vo/AdminTransactionVO.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/AdminTransactionServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/UserStatusTypeHandler.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/UserServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/CouponMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/Order.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/utils/JwtUtil.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/OrderMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AnnouncementController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/dto/RegisterRequest.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/dto/FaultReportRequest.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AdminAnnouncementController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/dto/LoginRequest.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/AdminOrderService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/dto/RechargeRequest.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/Transaction.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/vo/Result.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/RentController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/OperationLogMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/HealthController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/AdminUserServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/aspect/AdminAuthAspect.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/PasswordEncoderConfig.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/exception/BusinessException.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/vo/LoginResponse.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/vo/AdminOrderVO.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AdminFaultReportController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/CouponTypeHandler.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/TransactionMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/AnnouncementService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/PaymentService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AuthController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/vo/DashboardStatsResponse.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/dto/UpdateUserStatusRequest.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/Announcement.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/Coupon.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/UserCoupon.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/PaymentServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/aspect/OperationLogAspect.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/BillingRuleService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/VehicleServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/FaultReportMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/OperationLogServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/UserCouponStatusTypeHandler.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/AdminDashboardServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/config/FaultReportStatusTypeHandler.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/enums/UserStatus.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/mapper/UserMapper.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/VehicleRentalApplication.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/VehicleController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/SimulationService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/AdminUserService.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/exception/GlobalExceptionHandler.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/OperationLog.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/User.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/impl/RentServiceImpl.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/entity/FaultReport.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/enums/VehicleStatus.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/dto/AddVehicleRequest.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/dto/AnnouncementRequest.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/enums/FaultReportStatus.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/controller/AdminUserController.java
/Users/<USER>/项目/电动车/backend/src/main/java/com/campus/vehicle/service/RentService.java
