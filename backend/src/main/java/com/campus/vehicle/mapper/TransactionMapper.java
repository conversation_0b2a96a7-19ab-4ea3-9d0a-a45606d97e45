package com.campus.vehicle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.campus.vehicle.entity.Transaction;
import com.campus.vehicle.vo.AdminTransactionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TransactionMapper extends BaseMapper<Transaction> {
    
    /**
     * 使用JOIN查询交易列表，避免N+1问题
     */
    List<AdminTransactionVO> selectTransactionsWithUser(@Param("type") String type,
                                                        @Param("keyword") String keyword,
                                                        @Param("offset") long offset,
                                                        @Param("size") int size);
}