package com.campus.vehicle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.campus.vehicle.entity.Order;
import com.campus.vehicle.vo.AdminOrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrderMapper extends BaseMapper<Order> {
    
    /**
     * 使用JOIN查询订单列表，避免N+1问题
     */
    List<AdminOrderVO> selectOrdersWithUserAndVehicle(@Param("status") String status, 
                                                      @Param("keyword") String keyword,
                                                      @Param("offset") long offset, 
                                                      @Param("size") int size);
}