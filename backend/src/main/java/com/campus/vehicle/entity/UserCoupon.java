package com.campus.vehicle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.campus.vehicle.enums.UserCouponStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("user_coupons")
public class UserCoupon {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("coupon_id")
    private Long couponId;
    
    /**
     * 状态: UNUSED-未使用, USED-已使用, EXPIRED-已过期
     */
    @TableField(value = "status", typeHandler = com.campus.vehicle.config.UserCouponStatusTypeHandler.class)
    private UserCouponStatus status;
    
    @TableField("issue_time")
    private LocalDateTime issueTime;
    
    @TableField("expiry_time")
    private LocalDateTime expiryTime;

    // 构造函数中的默认值设置
    {
        this.status = UserCouponStatus.UNUSED;
        this.issueTime = LocalDateTime.now();
    }
}