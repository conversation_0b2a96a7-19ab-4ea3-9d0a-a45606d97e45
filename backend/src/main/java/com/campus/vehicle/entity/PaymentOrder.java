package com.campus.vehicle.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("payment_orders")
public class PaymentOrder {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("payment_order_sn")
    private String paymentOrderSn;
    
    @TableField("business_order_sn")
    private String businessOrderSn;
    
    @TableField("user_id")
    private Long userId;
    
    /**
     * 支付类型: RECHARGE-充值, DEPOSIT-押金
     */
    @TableField("payment_type")
    private String paymentType;
    
    @TableField("amount")
    private BigDecimal amount;
    
    /**
     * 支付状态: PENDING-待支付, SUCCESS-成功, FAILED-失败
     */
    @TableField("status")
    private String status = "PENDING";
    
    @TableField("alipay_trade_no")
    private String alipayTradeNo;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField("pay_time")
    private LocalDateTime payTime;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}