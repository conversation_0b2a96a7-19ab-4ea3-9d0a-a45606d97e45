package com.campus.vehicle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.campus.vehicle.enums.UserStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("users")
public class User {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("username")
    private String username;
    
    @TableField("password")
    private String password;
    
    @TableField("nickname")
    private String nickname;
    
    @TableField("phone")
    private String phone;
    
    @TableField("avatar_url")
    private String avatarUrl;
    
    @TableField("balance")
    private BigDecimal balance;
    
    @TableField("deposit")
    private BigDecimal deposit;
    
    /**
     * 用户状态: NORMAL-正常, FROZEN-冻结
     */
    @TableField(value = "status", typeHandler = com.campus.vehicle.config.UserStatusTypeHandler.class)
    private UserStatus status;
    
    /**
     * 用户角色: USER-普通用户, ADMIN-管理员
     */
    @TableField("role")
    private String role;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 构造函数中的默认值设置
    {
        this.balance = BigDecimal.ZERO;
        this.deposit = BigDecimal.ZERO;
        this.status = UserStatus.NORMAL;
        this.role = "USER";
    }
}