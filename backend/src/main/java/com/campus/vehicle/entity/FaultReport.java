package com.campus.vehicle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.campus.vehicle.enums.FaultReportStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("fault_reports")
public class FaultReport {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("vehicle_id")
    private Long vehicleId;
    
    @TableField("fault_type")
    private String faultType;
    
    @TableField("description")
    private String description;
    
    @TableField("image_urls")
    private String imageUrls;
    
    /**
     * 状态: PENDING-待处理, PROCESSING-处理中, RESOLVED-已解决
     */
    @TableField(value = "status", typeHandler = com.campus.vehicle.config.FaultReportStatusTypeHandler.class)
    private FaultReportStatus status;
    
    @TableField("report_time")
    private LocalDateTime reportTime;
    
    @TableField("resolve_time")
    private LocalDateTime resolveTime;

    // 构造函数中的默认值设置
    {
        this.status = FaultReportStatus.PENDING;
        this.reportTime = LocalDateTime.now();
    }
}