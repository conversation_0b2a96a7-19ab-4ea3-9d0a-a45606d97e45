package com.campus.vehicle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.campus.vehicle.enums.CouponType;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("coupons")
public class Coupon {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @NotBlank(message = "优惠券名称不能为空")
    @TableField("name")
    private String name;
    
    /**
     * 优惠券类型: DISCOUNT-折扣, DEDUCTION-抵扣
     */
    @TableField(value = "type", typeHandler = com.campus.vehicle.config.CouponTypeHandler.class)
    private CouponType type;
    
    /**
     * 优惠值: 折扣类型为折扣比例(0.8表示8折), 抵扣类型为抵扣金额
     */
    @NotNull(message = "优惠值不能为空")
    @Positive(message = "优惠值必须大于0")
    @TableField("value")
    private BigDecimal value;
    
    /**
     * 最低消费金额
     */
    @NotNull(message = "最低消费金额不能为空")
    @TableField("min_spend")
    private BigDecimal minSpend;
    
    /**
     * 有效天数
     */
    @NotNull(message = "有效天数不能为空")
    @Positive(message = "有效天数必须大于0")
    @TableField("valid_days")
    private Integer validDays;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}