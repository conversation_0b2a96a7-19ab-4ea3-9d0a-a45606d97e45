package com.campus.vehicle.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("billing_rules")
public class BillingRule {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 基础时长(分钟)
     */
    @NotNull(message = "基础时长不能为空")
    @Positive(message = "基础时长必须大于0")
    @TableField("base_duration")
    private Integer baseDuration;
    
    /**
     * 基础费用(元)
     */
    @NotNull(message = "基础费用不能为空")
    @Positive(message = "基础费用必须大于0")
    @TableField("base_fee")
    private BigDecimal baseFee;
    
    /**
     * 超时计费单位(分钟)
     */
    @NotNull(message = "超时计费单位不能为空")
    @Positive(message = "超时计费单位必须大于0")
    @TableField("extra_duration_unit")
    private Integer extraDurationUnit;
    
    /**
     * 超时单价(元)
     */
    @NotNull(message = "超时单价不能为空")
    @Positive(message = "超时单价必须大于0")
    @TableField("extra_fee_unit")
    private BigDecimal extraFeeUnit;
    
    /**
     * 是否激活
     */
    @TableField("is_active")
    private Boolean isActive = true;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}