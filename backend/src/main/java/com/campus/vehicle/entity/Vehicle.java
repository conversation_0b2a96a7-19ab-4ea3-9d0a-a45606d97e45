package com.campus.vehicle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.campus.vehicle.enums.VehicleStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("vehicles")
public class Vehicle {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("vehicle_sn")
    private String vehicleSn;
    
    /**
     * 车辆状态: AVAILABLE-可用, RENTED-租用中, FAULTY-故障
     */
    @TableField(value = "status", typeHandler = com.campus.vehicle.config.VehicleStatusTypeHandler.class)
    private VehicleStatus status;
    
    @TableField("battery")
    private Integer battery;
    
    /**
     * X坐标(百分比)
     */
    @TableField("position_x")
    private BigDecimal positionX;
    
    /**
     * Y坐标(百分比)
     */
    @TableField("position_y") 
    private BigDecimal positionY;
    
    @TableField("last_maintain_time")
    private LocalDateTime lastMaintainTime;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 构造函数中的默认值设置
    {
        this.status = VehicleStatus.AVAILABLE;
        this.battery = 100;
    }
}