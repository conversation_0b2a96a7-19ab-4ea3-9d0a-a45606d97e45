package com.campus.vehicle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.campus.vehicle.enums.OrderStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("orders")
public class Order {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("order_sn")
    private String orderSn;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("vehicle_id")
    private Long vehicleId;
    
    /**
     * 订单状态: RENTING-租用中, COMPLETED-已完成
     */
    @TableField(value = "status", typeHandler = com.campus.vehicle.config.OrderStatusTypeHandler.class)
    private OrderStatus status;
    
    @TableField("start_time")
    private LocalDateTime startTime;
    
    @TableField("end_time")
    private LocalDateTime endTime;
    
    /**
     * 租用时长(分钟)
     */
    @TableField("duration")
    private Integer duration;
    
    @TableField("base_fee")
    private BigDecimal baseFee;
    
    @TableField("discount")
    private BigDecimal discount;
    
    @TableField("final_fee")
    private BigDecimal finalFee;
    
    @TableField("user_coupon_id")
    private Long userCouponId;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 构造函数中的默认值设置
    {
        this.status = OrderStatus.RENTING;
        this.discount = BigDecimal.ZERO;
    }
}