package com.campus.vehicle.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("operation_logs")
public class OperationLog {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("operator_id")
    private Long operatorId;
    
    @TableField("operator_name")
    private String operatorName;
    
    @TableField("operation_type")
    private String operationType;
    
    @TableField("details")
    private String details;
    
    @TableField("ip_address")
    private String ipAddress;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}