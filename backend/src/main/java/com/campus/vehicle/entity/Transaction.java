package com.campus.vehicle.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("transactions")
public class Transaction {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    /**
     * 交易类型: RECHARGE-充值, RENT_PAYMENT-租车支付, DEPOSIT-押金, DEPOSIT_REFUND-退押金
     */
    @TableField("type")
    private String type;
    
    @TableField("amount")
    private BigDecimal amount;
    
    @TableField("related_order_sn")
    private String relatedOrderSn;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}