package com.campus.vehicle.aspect;

import com.campus.vehicle.annotation.OperationLog;
import com.campus.vehicle.service.OperationLogService;
import com.campus.vehicle.utils.JwtUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

@Aspect
@Component
public class OperationLogAspect {

    @Autowired
    private OperationLogService operationLogService;
    
    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 记录管理员操作日志 - 基于注解
     */
    @AfterReturning("@annotation(com.campus.vehicle.annotation.OperationLog)")
    public void logOperationWithAnnotation(JoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            OperationLog operationLog = method.getAnnotation(OperationLog.class);
            
            String operationDesc = operationLog.value();
            if (operationDesc.isEmpty()) {
                operationDesc = method.getName();
            }
            
            recordLog(joinPoint, operationDesc);
        } catch (Exception e) {
            // 记录日志失败不影响主业务
        }
    }

    /**
     * 记录管理员操作日志 - 基于类名拦截
     */
    @AfterReturning("execution(* com.campus.vehicle.controller.Admin*.*(..))")
    public void logAdminOperation(JoinPoint joinPoint) {
        try {
            // 检查方法是否已经有@OperationLog注解，避免重复记录
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            if (method.isAnnotationPresent(OperationLog.class)) {
                return; // 已经通过注解记录了，跳过
            }
            
            String className = joinPoint.getTarget().getClass().getSimpleName();
            String methodName = joinPoint.getSignature().getName();
            String operationType = className + "." + methodName;
            
            recordLog(joinPoint, operationType);
        } catch (Exception e) {
            // 记录日志失败不影响主业务
        }
    }

    /**
     * 通用记录日志方法
     */
    private void recordLog(JoinPoint joinPoint, String operationType) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return;
        }
        
        HttpServletRequest request = attributes.getRequest();
        String token = request.getHeader("Authorization");
        
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            String username = jwtUtil.getUsernameFromToken(token);
            Long userId = jwtUtil.getUserIdFromToken(token);
            
            String details = request.getMethod() + " " + request.getRequestURI();
            String ipAddress = getClientIpAddress(request);
            
            operationLogService.recordLog(userId, username, operationType, details, ipAddress);
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}