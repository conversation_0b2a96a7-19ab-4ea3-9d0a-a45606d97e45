package com.campus.vehicle.aspect;

import com.campus.vehicle.annotation.RequireAdmin;
import com.campus.vehicle.constant.BusinessConstants;
import com.campus.vehicle.entity.User;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.service.UserService;
import com.campus.vehicle.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 管理员权限校验切面
 */
@Aspect
@Component
@Slf4j
public class AdminAuthAspect {

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private UserService userService;

    /**
     * 环绕通知，拦截标有@RequireAdmin注解的方法
     */
    @Around("@annotation(requireAdmin)")
    public Object checkAdminPermission(ProceedingJoinPoint joinPoint, RequireAdmin requireAdmin) throws Throwable {
        try {
            // 获取当前HTTP请求
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                throw new BusinessException("无法获取当前请求上下文");
            }
            
            HttpServletRequest request = attributes.getRequest();
            
            // 从请求头中获取Token
            String token = getTokenFromRequest(request);
            if (token == null) {
                throw new BusinessException("未提供认证令牌");
            }
            
            // 从Token中获取用户ID
            Long userId;
            try {
                userId = jwtUtil.getUserIdFromToken(token);
            } catch (Exception e) {
                throw new BusinessException("认证令牌无效");
            }
            
            // 查询用户信息
            User user = userService.findById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 校验用户角色
            if (!BusinessConstants.UserRole.ADMIN.equals(user.getRole())) {
                log.warn("用户[{}]尝试访问管理员接口: {}", user.getUsername(), joinPoint.getSignature().getName());
                throw new BusinessException("权限不足，需要管理员权限");
            }
            
            // 权限校验通过，执行目标方法
            log.debug("管理员[{}]访问接口: {}", user.getUsername(), joinPoint.getSignature().getName());
            return joinPoint.proceed();
            
        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("权限校验过程中发生异常", e);
            throw new BusinessException("权限校验失败: " + e.getMessage());
        }
    }

    /**
     * 从请求中获取JWT Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }
}