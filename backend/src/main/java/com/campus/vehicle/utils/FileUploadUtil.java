package com.campus.vehicle.utils;

import com.campus.vehicle.exception.BusinessException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Component
public class FileUploadUtil {

    @Value("${file.upload.path:/uploads/}")
    private String uploadPath;

    @Value("${file.upload.max-size:5242880}") // 5MB
    private long maxFileSize;

    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    );

    /**
     * 上传文件
     */
    public String uploadFile(MultipartFile file, String subDir) {
        // 检查文件
        validateFile(file);
        
        // 创建上传目录
        String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String fullPath = uploadPath + subDir + "/" + dateDir;
        File uploadDir = new File(fullPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }

        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String filename = UUID.randomUUID().toString() + extension;

        try {
            // 保存文件
            File targetFile = new File(uploadDir, filename);
            file.transferTo(targetFile);
            
            // 返回访问路径（添加/api前缀，与静态资源配置匹配）
            return "/api/" + subDir + "/" + dateDir + "/" + filename;
        } catch (IOException e) {
            throw new BusinessException("文件上传失败");
        }
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("请选择要上传的文件");
        }

        if (file.getSize() > maxFileSize) {
            throw new BusinessException("文件大小不能超过5MB");
        }

        String contentType = file.getContentType();
        if (!ALLOWED_IMAGE_TYPES.contains(contentType)) {
            throw new BusinessException("只支持上传JPG、PNG、GIF、WEBP格式的图片");
        }
    }
}