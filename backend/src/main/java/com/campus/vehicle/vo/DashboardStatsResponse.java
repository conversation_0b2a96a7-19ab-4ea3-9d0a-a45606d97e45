package com.campus.vehicle.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class DashboardStatsResponse {
    
    /**
     * 用户总数
     */
    private Long totalUsers;
    
    /**
     * 车辆总数
     */
    private Long totalVehicles;
    
    /**
     * 当前租赁订单数
     */
    private Long currentRentingOrders;
    
    /**
     * 故障车辆数
     */
    private Long faultVehicles;
    
    /**
     * 今日收入
     */
    private BigDecimal todayRevenue;
    
    /**
     * 可用车辆数
     */
    private Long availableVehicles;
    
    /**
     * 待处理故障报告数
     */
    private Long pendingFaultReports;
}