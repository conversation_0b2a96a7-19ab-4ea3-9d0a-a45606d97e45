package com.campus.vehicle.vo;

import com.campus.vehicle.enums.OrderStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class AdminOrderVO {
    
    private Long id;
    
    private String orderSn;
    
    private Long userId;
    
    private String username;
    
    private String userPhone;
    
    private Long vehicleId;
    
    private String vehicleCode;
    
    private OrderStatus status;
    
    private LocalDateTime startTime;
    
    private LocalDateTime endTime;
    
    private Integer duration;
    
    private BigDecimal baseFee;
    
    private BigDecimal discount;
    
    private BigDecimal finalFee;
    
    private LocalDateTime createTime;
}