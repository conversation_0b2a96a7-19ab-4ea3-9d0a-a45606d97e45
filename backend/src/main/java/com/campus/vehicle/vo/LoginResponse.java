package com.campus.vehicle.vo;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class LoginResponse {
    
    private String token;
    private UserInfo userInfo;
    
    public LoginResponse(String token, UserInfo userInfo) {
        this.token = token;
        this.userInfo = userInfo;
    }

    @Data
    public static class UserInfo {
        private Long id;
        private String username;
        private String nickname;
        private String phone;
        private String avatarUrl;
        private BigDecimal balance;
        private BigDecimal deposit;
        private String status;
        private String role;
    }
}