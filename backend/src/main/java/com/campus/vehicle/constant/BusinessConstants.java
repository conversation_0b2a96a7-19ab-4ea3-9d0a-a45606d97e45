package com.campus.vehicle.constant;

/**
 * 业务常量类
 */
public class BusinessConstants {
    
    /**
     * 用户角色常量
     */
    public static class UserRole {
        public static final String USER = "USER";
        public static final String ADMIN = "ADMIN";
    }
    
    /**
     * 交易类型常量
     */
    public static class TransactionType {
        public static final String RECHARGE = "RECHARGE";
        public static final String RENT_PAYMENT = "RENT_PAYMENT";
        public static final String DEPOSIT = "DEPOSIT";
        public static final String DEPOSIT_REFUND = "DEPOSIT_REFUND";
    }
    
    /**
     * 交易类型描述常量
     */
    public static class TransactionTypeDesc {
        public static final String RECHARGE_DESC = "余额充值";
        public static final String RENT_PAYMENT_DESC = "租车支付";
        public static final String DEPOSIT_DESC = "押金缴纳";
        public static final String DEPOSIT_REFUND_DESC = "押金退还";
    }
}