package com.campus.vehicle.config;

import com.campus.vehicle.enums.UserCouponStatus;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class UserCouponStatusTypeHandler extends BaseTypeHandler<UserCouponStatus> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, UserCouponStatus parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.name());
    }

    @Override
    public UserCouponStatus getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String status = rs.getString(columnName);
        return status == null ? null : UserCouponStatus.valueOf(status);
    }

    @Override
    public UserCouponStatus getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String status = rs.getString(columnIndex);
        return status == null ? null : UserCouponStatus.valueOf(status);
    }

    @Override
    public UserCouponStatus getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String status = cs.getString(columnIndex);
        return status == null ? null : UserCouponStatus.valueOf(status);
    }
}