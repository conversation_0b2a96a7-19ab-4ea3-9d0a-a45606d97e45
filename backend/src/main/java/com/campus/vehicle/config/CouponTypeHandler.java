package com.campus.vehicle.config;

import com.campus.vehicle.enums.CouponType;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class CouponTypeHandler extends BaseTypeHandler<CouponType> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, CouponType parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.name());
    }

    @Override
    public CouponType getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String type = rs.getString(columnName);
        return type == null ? null : CouponType.valueOf(type);
    }

    @Override
    public CouponType getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String type = rs.getString(columnIndex);
        return type == null ? null : CouponType.valueOf(type);
    }

    @Override
    public CouponType getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String type = cs.getString(columnIndex);
        return type == null ? null : CouponType.valueOf(type);
    }
}