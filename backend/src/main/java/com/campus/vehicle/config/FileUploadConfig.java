package com.campus.vehicle.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class FileUploadConfig implements WebMvcConfigurer {

    @Value("${file.upload.path:/uploads/}")
    private String uploadPath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置文件访问路径，支持API上下文路径
        registry.addResourceHandler("/api/avatars/**")
                .addResourceLocations("file:" + uploadPath + "avatars/");
        
        registry.addResourceHandler("/api/images/**")
                .addResourceLocations("file:" + uploadPath + "images/");
                
        // 兼容直接访问（可选）
        registry.addResourceHandler("/avatars/**")
                .addResourceLocations("file:" + uploadPath + "avatars/");
        
        registry.addResourceHandler("/images/**")
                .addResourceLocations("file:" + uploadPath + "images/");
    }
}