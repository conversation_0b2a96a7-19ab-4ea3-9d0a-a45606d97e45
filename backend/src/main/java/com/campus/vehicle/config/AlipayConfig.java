package com.campus.vehicle.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "alipay")
public class AlipayConfig {
    
    private String appId;
    private String merchantPrivateKey;
    private String alipayPublicKey;
    private String signType;
    private String charset;
    private String format;
    private String gatewayUrl;
    private String notifyUrl;

}