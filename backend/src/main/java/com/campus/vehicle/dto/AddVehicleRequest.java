package com.campus.vehicle.dto;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.math.BigDecimal;

@Data
public class AddVehicleRequest {
    
    @DecimalMin(value = "0.0", message = "X坐标不能小于0")
    @DecimalMax(value = "100.0", message = "X坐标不能大于100")
    private BigDecimal positionX;
    
    @DecimalMin(value = "0.0", message = "Y坐标不能小于0") 
    @DecimalMax(value = "100.0", message = "Y坐标不能大于100")
    private BigDecimal positionY;
    
    @Min(value = 0, message = "电量不能小于0")
    @Max(value = 100, message = "电量不能大于100")
    private Integer battery = 100;
}