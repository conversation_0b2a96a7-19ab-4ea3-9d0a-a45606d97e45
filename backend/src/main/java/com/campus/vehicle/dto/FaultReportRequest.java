package com.campus.vehicle.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class FaultReportRequest {
    
    @NotNull(message = "车辆ID不能为空")
    private Long vehicleId;
    
    @NotBlank(message = "故障类型不能为空")
    private String faultType;
    
    @NotBlank(message = "故障描述不能为空")
    private String description;
    
    private String imageUrls;
}