package com.campus.vehicle.exception;

import com.campus.vehicle.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final String REQUEST_FORMAT = "[%s] %s";

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Void> handleBusinessException(BusinessException e, HttpServletRequest request) {
        String requestInfo = String.format(REQUEST_FORMAT, request.getMethod(), request.getRequestURI());
        log.warn("业务异常: {} - 请求: {}, 错误码: {}, 错误信息: {}", 
            requestInfo, request.getRemoteAddr(), e.getCode(), e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleValidationException(Exception e, HttpServletRequest request) {
        String requestInfo = String.format(REQUEST_FORMAT, request.getMethod(), request.getRequestURI());
        
        List<String> errorMessages;
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            errorMessages = ex.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.toList());
        } else if (e instanceof BindException) {
            BindException ex = (BindException) e;
            errorMessages = ex.getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.toList());
        } else {
            errorMessages = java.util.Collections.singletonList("参数校验失败");
        }
        
        String errorDetail = String.join(", ", errorMessages);
        log.warn("参数校验异常: {} - 请求来源: {}, 详细错误: {}", 
            requestInfo, request.getRemoteAddr(), errorDetail);
            
        return Result.error(400, "参数校验失败: " + errorDetail);
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleException(Exception e, HttpServletRequest request) {
        String requestInfo = String.format(REQUEST_FORMAT, request.getMethod(), request.getRequestURI());
        
        // 记录完整的异常堆栈和请求上下文
        if (log.isErrorEnabled()) {
            log.error("系统异常: {} - 请求来源: {}, 用户Agent: {}, 异常类型: {}, 错误信息: {}", 
            requestInfo, 
            request.getRemoteAddr(),
            request.getHeader("User-Agent"),
            e.getClass().getSimpleName(), 
            e.getMessage(), 
            e);
        }
            
        return Result.error("系统异常，请联系管理员");
    }
}