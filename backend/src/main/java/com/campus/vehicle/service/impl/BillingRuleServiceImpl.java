package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.campus.vehicle.entity.BillingRule;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.BillingRuleMapper;
import com.campus.vehicle.service.BillingRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Service
public class BillingRuleServiceImpl implements BillingRuleService {

    @Autowired
    private BillingRuleMapper billingRuleMapper;

    @Override
    public BillingRule getCurrentBillingRule() {
        LambdaQueryWrapper<BillingRule> query = new LambdaQueryWrapper<>();
        query.eq(BillingRule::getIsActive, true);
        BillingRule rule = billingRuleMapper.selectOne(query);
        
        if (rule == null) {
            // 返回默认计费规则
            rule = new BillingRule();
            rule.setBaseDuration(15);
            rule.setBaseFee(new BigDecimal("2.00"));
            rule.setExtraDurationUnit(5);
            rule.setExtraFeeUnit(new BigDecimal("1.00"));
            rule.setIsActive(true);
        }
        
        return rule;
    }

    @Override
    @Transactional
    public void updateBillingRule(BillingRule billingRule) {
        // 先将所有规则设为非活跃
        BillingRule updateAll = new BillingRule();
        updateAll.setIsActive(false);
        LambdaQueryWrapper<BillingRule> queryAll = new LambdaQueryWrapper<>();
        queryAll.eq(BillingRule::getIsActive, true);
        billingRuleMapper.update(updateAll, queryAll);
        
        // 插入新规则或更新现有规则
        if (billingRule.getId() != null) {
            billingRule.setIsActive(true);
            int result = billingRuleMapper.updateById(billingRule);
            if (result != 1) {
                throw new BusinessException("更新计费规则失败");
            }
        } else {
            billingRule.setIsActive(true);
            int result = billingRuleMapper.insert(billingRule);
            if (result != 1) {
                throw new BusinessException("创建计费规则失败");
            }
        }
    }
}