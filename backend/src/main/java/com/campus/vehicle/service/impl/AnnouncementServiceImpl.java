package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.AnnouncementRequest;
import com.campus.vehicle.entity.Announcement;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.AnnouncementMapper;
import com.campus.vehicle.service.AnnouncementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AnnouncementServiceImpl implements AnnouncementService {

    @Autowired
    private AnnouncementMapper announcementMapper;

    @Override
    public List<Announcement> getLatestAnnouncements(int limit) {
        LambdaQueryWrapper<Announcement> query = new LambdaQueryWrapper<>();
        query.orderByDesc(Announcement::getCreateTime)
             .last("LIMIT " + limit);
        return announcementMapper.selectList(query);
    }

    @Override
    public Page<Announcement> getAnnouncementList(int current, int size) {
        Page<Announcement> page = new Page<>(current, size);
        LambdaQueryWrapper<Announcement> query = new LambdaQueryWrapper<>();
        query.orderByDesc(Announcement::getCreateTime);
        return announcementMapper.selectPage(page, query);
    }

    @Override
    public void createAnnouncement(Long authorId, AnnouncementRequest request) {
        Announcement announcement = new Announcement();
        announcement.setTitle(request.getTitle());
        announcement.setContent(request.getContent());
        announcement.setAuthorId(authorId);
        
        int result = announcementMapper.insert(announcement);
        if (result != 1) {
            throw new BusinessException("创建公告失败");
        }
    }

    @Override
    public void updateAnnouncement(Long id, AnnouncementRequest request) {
        Announcement announcement = announcementMapper.selectById(id);
        if (announcement == null) {
            throw new BusinessException("公告不存在");
        }
        
        Announcement updateAnnouncement = new Announcement();
        updateAnnouncement.setId(id);
        updateAnnouncement.setTitle(request.getTitle());
        updateAnnouncement.setContent(request.getContent());
        
        int result = announcementMapper.updateById(updateAnnouncement);
        if (result != 1) {
            throw new BusinessException("更新公告失败");
        }
    }

    @Override
    public void deleteAnnouncement(Long id) {
        int result = announcementMapper.deleteById(id);
        if (result != 1) {
            throw new BusinessException("删除公告失败");
        }
    }

    @Override
    public Announcement getById(Long id) {
        Announcement announcement = announcementMapper.selectById(id);
        if (announcement == null) {
            throw new BusinessException("公告不存在");
        }
        return announcement;
    }
}