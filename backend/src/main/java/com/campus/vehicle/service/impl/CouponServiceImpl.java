package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.campus.vehicle.entity.Coupon;
import com.campus.vehicle.entity.UserCoupon;
import com.campus.vehicle.enums.CouponType;
import com.campus.vehicle.enums.UserCouponStatus;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.CouponMapper;
import com.campus.vehicle.mapper.UserCouponMapper;
import com.campus.vehicle.service.CouponService;
import com.campus.vehicle.vo.UserCouponVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CouponServiceImpl implements CouponService {

    @Autowired
    private UserCouponMapper userCouponMapper;
    
    @Autowired
    private CouponMapper couponMapper;

    @Override
    public List<UserCouponVO> getUserCoupons(Long userId) {
        LambdaQueryWrapper<UserCoupon> query = new LambdaQueryWrapper<>();
        query.eq(UserCoupon::getUserId, userId)
             .orderByDesc(UserCoupon::getIssueTime);
        
        List<UserCoupon> userCoupons = userCouponMapper.selectList(query);
        
        return userCoupons.stream().map(userCoupon -> {
            UserCouponVO vo = new UserCouponVO();
            BeanUtils.copyProperties(userCoupon, vo);
            
            // 设置状态描述
            vo.setStatus(userCoupon.getStatus().getDescription());
            
            // 获取优惠券详情
            Coupon coupon = couponMapper.selectById(userCoupon.getCouponId());
            if (coupon != null) {
                vo.setCouponName(coupon.getName());
                vo.setType(coupon.getType().getDescription());
                vo.setValue(coupon.getValue());
                vo.setMinSpend(coupon.getMinSpend());
            }
            
            // 检查过期状态
            if (userCoupon.getStatus() == UserCouponStatus.UNUSED && 
                userCoupon.getExpiryTime().isBefore(LocalDateTime.now())) {
                vo.setStatus(UserCouponStatus.EXPIRED.getDescription());
                // 更新数据库状态
                UserCoupon updateCoupon = new UserCoupon();
                updateCoupon.setId(userCoupon.getId());
                updateCoupon.setStatus(UserCouponStatus.EXPIRED);
                userCouponMapper.updateById(updateCoupon);
            }
            
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public BigDecimal calculateDiscount(Long userCouponId, BigDecimal originalAmount) {
        UserCoupon userCoupon = userCouponMapper.selectById(userCouponId);
        if (userCoupon == null) {
            return BigDecimal.ZERO;
        }
        
        if (!isCouponUsable(userCouponId, originalAmount)) {
            return BigDecimal.ZERO;
        }
        
        Coupon coupon = couponMapper.selectById(userCoupon.getCouponId());
        if (coupon == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal discount = BigDecimal.ZERO;
        
        if (coupon.getType() == CouponType.DISCOUNT) {
            // 折扣券: 原价 * (1 - 折扣比例)
            discount = originalAmount.multiply(BigDecimal.ONE.subtract(coupon.getValue()));
        } else if (coupon.getType() == CouponType.DEDUCTION) {
            // 抵扣券: 直接减去抵扣金额，但不能超过原价
            discount = coupon.getValue().min(originalAmount);
        }
        
        return discount.setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public void useCoupon(Long userCouponId) {
        UserCoupon userCoupon = userCouponMapper.selectById(userCouponId);
        if (userCoupon == null) {
            throw new BusinessException("优惠券不存在");
        }
        
        if (userCoupon.getStatus() != UserCouponStatus.UNUSED) {
            throw new BusinessException("优惠券已使用或已过期");
        }
        
        UserCoupon updateCoupon = new UserCoupon();
        updateCoupon.setId(userCouponId);
        updateCoupon.setStatus(UserCouponStatus.USED);
        
        int result = userCouponMapper.updateById(updateCoupon);
        if (result != 1) {
            throw new BusinessException("使用优惠券失败");
        }
    }

    @Override
    public void issueCoupon(Long userId, Long couponId) {
        Coupon coupon = couponMapper.selectById(couponId);
        if (coupon == null) {
            throw new BusinessException("优惠券模板不存在");
        }
        
        UserCoupon userCoupon = new UserCoupon();
        userCoupon.setUserId(userId);
        userCoupon.setCouponId(couponId);
        userCoupon.setExpiryTime(LocalDateTime.now().plusDays(coupon.getValidDays()));
        
        int result = userCouponMapper.insert(userCoupon);
        if (result != 1) {
            throw new BusinessException("发放优惠券失败");
        }
    }

    @Override
    public boolean isCouponUsable(Long userCouponId, BigDecimal amount) {
        UserCoupon userCoupon = userCouponMapper.selectById(userCouponId);
        if (userCoupon == null) {
            return false;
        }
        
        // 检查状态
        if (userCoupon.getStatus() != UserCouponStatus.UNUSED) {
            return false;
        }
        
        // 检查过期时间
        if (userCoupon.getExpiryTime().isBefore(LocalDateTime.now())) {
            return false;
        }
        
        // 检查最低消费
        Coupon coupon = couponMapper.selectById(userCoupon.getCouponId());
        if (coupon == null) {
            return false;
        }
        
        return amount.compareTo(coupon.getMinSpend()) >= 0;
    }
}