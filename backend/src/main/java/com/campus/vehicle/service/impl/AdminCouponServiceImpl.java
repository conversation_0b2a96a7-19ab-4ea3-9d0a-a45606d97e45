package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.entity.Coupon;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.CouponMapper;
import com.campus.vehicle.service.AdminCouponService;
import com.campus.vehicle.service.CouponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class AdminCouponServiceImpl implements AdminCouponService {

    @Autowired
    private CouponMapper couponMapper;
    
    @Autowired
    private CouponService couponService;

    @Override
    public Page<Coupon> getCouponList(int current, int size) {
        Page<Coupon> page = new Page<>(current, size);
        return couponMapper.selectPage(page, null);
    }

    @Override
    public void createCoupon(Coupon coupon) {
        int result = couponMapper.insert(coupon);
        if (result != 1) {
            throw new BusinessException("创建优惠券模板失败");
        }
    }

    @Override
    public void updateCoupon(Coupon coupon) {
        Coupon existingCoupon = couponMapper.selectById(coupon.getId());
        if (existingCoupon == null) {
            throw new BusinessException("优惠券模板不存在");
        }
        
        int result = couponMapper.updateById(coupon);
        if (result != 1) {
            throw new BusinessException("更新优惠券模板失败");
        }
    }

    @Override
    public void deleteCoupon(Long id) {
        Coupon coupon = couponMapper.selectById(id);
        if (coupon == null) {
            throw new BusinessException("优惠券模板不存在");
        }
        
        int result = couponMapper.deleteById(id);
        if (result != 1) {
            throw new BusinessException("删除优惠券模板失败");
        }
    }

    @Override
    @Transactional
    public void batchIssueCoupon(List<Long> userIds, Long couponId) {
        if (userIds == null || userIds.isEmpty()) {
            throw new BusinessException("用户ID列表不能为空");
        }
        
        for (Long userId : userIds) {
            try {
                couponService.issueCoupon(userId, couponId);
            } catch (Exception e) {
                throw new BusinessException("给用户 " + userId + " 发放优惠券失败: " + e.getMessage());
            }
        }
    }
}