package com.campus.vehicle.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.EndRentRequest;
import com.campus.vehicle.dto.StartRentRequest;
import com.campus.vehicle.entity.Order;
import java.math.BigDecimal;

public interface RentService {
    
    /**
     * 开始租车
     */
    Order startRent(Long userId, StartRentRequest request);
    
    /**
     * 结束租车
     */
    void endRent(Long userId, EndRentRequest request);
    
    /**
     * 计算费用
     */
    BigDecimal calculateFee(Order order);
    
    /**
     * 获取用户当前租车订单
     */
    Order getCurrentRentOrder(Long userId);
    /**
     * 获取用户历史订单
     */
    Page<Order> getUserTrips(Long userId, int current, int size);
}