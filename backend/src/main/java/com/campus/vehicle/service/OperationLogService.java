package com.campus.vehicle.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.entity.OperationLog;

public interface OperationLogService {
    
    /**
     * 记录操作日志
     */
    void recordLog(Long operatorId, String operatorName, String operationType, 
                   String details, String ipAddress);
    
    /**
     * 分页获取操作日志
     */
    Page<OperationLog> getOperationLogs(int current, int size, String operationType);
}