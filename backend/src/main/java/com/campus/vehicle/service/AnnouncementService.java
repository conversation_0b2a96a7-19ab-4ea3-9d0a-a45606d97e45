package com.campus.vehicle.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.AnnouncementRequest;
import com.campus.vehicle.entity.Announcement;

import java.util.List;

public interface AnnouncementService {
    
    /**
     * 获取最新公告
     */
    List<Announcement> getLatestAnnouncements(int limit);
    
    /**
     * 分页获取公告列表
     */
    Page<Announcement> getAnnouncementList(int current, int size);
    
    /**
     * 创建公告
     */
    void createAnnouncement(Long authorId, AnnouncementRequest request);
    
    /**
     * 更新公告
     */
    void updateAnnouncement(Long id, AnnouncementRequest request);
    
    /**
     * 删除公告
     */
    void deleteAnnouncement(Long id);
    
    /**
     * 根据ID获取公告
     */
    Announcement getById(Long id);
}