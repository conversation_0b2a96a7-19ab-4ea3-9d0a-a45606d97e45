package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.constant.BusinessConstants;
import com.campus.vehicle.entity.User;
import com.campus.vehicle.enums.UserStatus;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.UserMapper;
import com.campus.vehicle.service.AdminUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AdminUserServiceImpl implements AdminUserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public Page<User> getUserList(int current, int size, String keyword) {
        Page<User> page = new Page<>(current, size);
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        
        // 只查询普通用户，不查询管理员
        queryWrapper.eq("role", BusinessConstants.UserRole.USER);
        
        // 根据关键词搜索
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("username", keyword)
                .or().like("nickname", keyword)
                .or().like("phone", keyword)
            );
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc("create_time");
        
        return userMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional
    public void updateUserStatus(Long userId, UserStatus status) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        if (!BusinessConstants.UserRole.USER.equals(user.getRole())) {
            throw new BusinessException("不能修改管理员状态");
        }
        
        user.setStatus(status);
        userMapper.updateById(user);
    }
}