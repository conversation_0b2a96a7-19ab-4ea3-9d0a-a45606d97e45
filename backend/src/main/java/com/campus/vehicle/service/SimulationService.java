package com.campus.vehicle.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.campus.vehicle.entity.Vehicle;
import com.campus.vehicle.enums.VehicleStatus;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.VehicleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Random;

@Slf4j
@Service
public class SimulationService {
    
    @Autowired
    private VehicleMapper vehicleMapper;
    
    private final Random random = new Random();

    /**
     * 仿真引擎核心：每3秒更新租用中车辆的位置和电量
     */
    @Scheduled(fixedRate = 3000)
    public void simulateVehicleMovement() {
        long startTime = System.currentTimeMillis();
        
        try {
            // 查询所有租用中的车辆
            LambdaQueryWrapper<Vehicle> query = new LambdaQueryWrapper<>();
            query.eq(Vehicle::getStatus, VehicleStatus.RENTED);
            List<Vehicle> rentedVehicles = vehicleMapper.selectList(query);
            
            if (rentedVehicles.isEmpty()) {
                log.debug("当前没有租用中的车辆，跳过仿真更新");
                return;
            }
            
            log.debug("开始仿真更新 {} 辆租用中的车辆", rentedVehicles.size());
            
            int successCount = 0;
            int failCount = 0;
            
            // 批量更新车辆位置和电量
            for (Vehicle vehicle : rentedVehicles) {
                try {
                    updateVehicleSimulation(vehicle);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    log.error("更新车辆{}仿真数据失败: {}", vehicle.getVehicleSn(), e.getMessage(), e);
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (failCount > 0) {
                log.warn("仿真更新完成：总数={}, 成功={}, 失败={}, 耗时={}ms", 
                    rentedVehicles.size(), successCount, failCount, duration);
            } else {
                log.debug("仿真更新完成：更新{}辆车辆，耗时={}ms", successCount, duration);
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("仿真引擎执行异常，耗时={}ms", duration, e);
        }
    }

    /**
     * 更新单个车辆的仿真数据
     */
    private void updateVehicleSimulation(Vehicle vehicle) {
        try {
            // 记录原始状态
            BigDecimal originalX = vehicle.getPositionX();
            BigDecimal originalY = vehicle.getPositionY();
            Integer originalBattery = vehicle.getBattery();
            
            // 模拟位置变化：在当前位置基础上随机移动
            BigDecimal newX = simulatePositionChange(vehicle.getPositionX());
            BigDecimal newY = simulatePositionChange(vehicle.getPositionY());
            
            // 模拟电量消耗：每次减少0.1-0.3
            Integer newBattery = simulateBatteryConsumption(vehicle.getBattery());
            
            // 检查电量过低情况
            if (newBattery <= 10) {
                log.warn("车辆{}电量过低：{}%，建议提醒用户充电", vehicle.getVehicleSn(), newBattery);
            }
            
            // 更新数据库
            Vehicle updateVehicle = new Vehicle();
            updateVehicle.setId(vehicle.getId());
            updateVehicle.setPositionX(newX);
            updateVehicle.setPositionY(newY);
            updateVehicle.setBattery(newBattery);
            
            int result = vehicleMapper.updateById(updateVehicle);
            if (result != 1) {
                log.error("更新车辆{}数据库记录失败", vehicle.getVehicleSn());
                throw new BusinessException("车辆仿真数据更新失败");
            }
            
            // 计算移动距离（简单的欧几里得距离）
            double distance = Math.sqrt(
                Math.pow(newX.subtract(originalX).doubleValue(), 2) + 
                Math.pow(newY.subtract(originalY).doubleValue(), 2)
            );
            
            log.debug("车辆{}仿真更新成功: 位置({}, {}) -> ({}, {}), 电量{}% -> {}%, 移动距离={:.2f}", 
                vehicle.getVehicleSn(), 
                originalX, originalY, newX, newY,
                originalBattery, newBattery, distance);
                
        } catch (Exception e) {
            log.error("更新车辆{}仿真数据失败: {}", vehicle.getVehicleSn(), e.getMessage());
            throw e;
        }
    }

    /**
     * 模拟位置变化
     */
    private BigDecimal simulatePositionChange(BigDecimal currentPosition) {
        // 在当前位置基础上随机移动 -2 到 +2 个百分比点
        double change = (random.nextDouble() - 0.5) * 4; // -2 到 +2
        BigDecimal newPosition = currentPosition.add(new BigDecimal(change));
        
        // 确保坐标在 0-100 范围内
        if (newPosition.compareTo(BigDecimal.ZERO) < 0) {
            newPosition = BigDecimal.ZERO;
        } else if (newPosition.compareTo(new BigDecimal("100")) > 0) {
            newPosition = new BigDecimal("100");
        }
        
        return newPosition.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 模拟电量消耗
     */
    private Integer simulateBatteryConsumption(Integer currentBattery) {
        // 每次消耗0.1-0.3的电量
        double consumption = 0.1 + (random.nextDouble() * 0.2);
        int newBattery = (int) (currentBattery - consumption);
        
        // 电量不能低于0
        return Math.max(newBattery, 0);
    }
}