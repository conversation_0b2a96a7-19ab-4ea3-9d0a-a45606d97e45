package com.campus.vehicle.service;

import com.campus.vehicle.vo.UserCouponVO;

import java.math.BigDecimal;
import java.util.List;

public interface CouponService {
    
    /**
     * 获取用户的优惠券列表
     */
    List<UserCouponVO> getUserCoupons(Long userId);
    
    /**
     * 计算优惠券折扣金额
     */
    BigDecimal calculateDiscount(Long userCouponId, BigDecimal originalAmount);
    
    /**
     * 使用优惠券
     */
    void useCoupon(Long userCouponId);
    
    /**
     * 给用户发放优惠券
     */
    void issueCoupon(Long userId, Long couponId);
    
    /**
     * 检查优惠券是否可用
     */
    boolean isCouponUsable(Long userCouponId, BigDecimal amount);
}