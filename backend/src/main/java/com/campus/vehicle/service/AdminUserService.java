package com.campus.vehicle.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.entity.User;
import com.campus.vehicle.enums.UserStatus;

public interface AdminUserService {
    
    /**
     * 分页查询用户列表
     */
    Page<User> getUserList(int current, int size, String keyword);
    
    /**
     * 更新用户状态
     */
    void updateUserStatus(Long userId, UserStatus status);
}