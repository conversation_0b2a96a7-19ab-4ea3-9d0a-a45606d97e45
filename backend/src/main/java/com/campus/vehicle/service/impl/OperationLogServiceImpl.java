package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.entity.OperationLog;
import com.campus.vehicle.mapper.OperationLogMapper;
import com.campus.vehicle.service.OperationLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Override
    @Async
    public void recordLog(Long operatorId, String operatorName, String operationType,
                         String details, String ipAddress) {
        try {
            OperationLog log = new OperationLog();
            log.setOperatorId(operatorId);
            log.setOperatorName(operatorName);
            log.setOperationType(operationType);
            log.setDetails(details);
            log.setIpAddress(ipAddress);
            
            operationLogMapper.insert(log);
        } catch (Exception e) {
            // 记录日志失败不影响主业务
        }
    }

    @Override
    public Page<OperationLog> getOperationLogs(int current, int size, String operationType) {
        Page<OperationLog> page = new Page<>(current, size);
        LambdaQueryWrapper<OperationLog> query = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(operationType)) {
            query.eq(OperationLog::getOperationType, operationType);
        }
        
        query.orderByDesc(OperationLog::getCreateTime);
        return operationLogMapper.selectPage(page, query);
    }
}