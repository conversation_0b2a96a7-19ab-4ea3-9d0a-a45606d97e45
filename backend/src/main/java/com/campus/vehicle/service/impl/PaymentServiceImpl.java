package com.campus.vehicle.service.impl;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.campus.vehicle.config.AlipayConfig;
import com.campus.vehicle.dto.RechargeRequest;
import com.campus.vehicle.entity.Order;
import com.campus.vehicle.entity.PaymentOrder;
import com.campus.vehicle.entity.Transaction;
import com.campus.vehicle.entity.User;
import com.campus.vehicle.enums.OrderStatus;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.OrderMapper;
import com.campus.vehicle.mapper.PaymentOrderMapper;
import com.campus.vehicle.mapper.TransactionMapper;
import com.campus.vehicle.mapper.UserMapper;
import com.campus.vehicle.service.PaymentService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
public class PaymentServiceImpl implements PaymentService {
    
    @Autowired
    private AlipayConfig alipayConfig;
    
    @Autowired
    private PaymentOrderMapper paymentOrderMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private TransactionMapper transactionMapper;
    
    @Autowired
    private OrderMapper orderMapper;

    @Override
    public String createRechargeOrder(Long userId, RechargeRequest request) {
        // 创建支付订单
        PaymentOrder paymentOrder = new PaymentOrder();
        paymentOrder.setPaymentOrderSn(generatePaymentOrderSn());
        paymentOrder.setUserId(userId);
        paymentOrder.setPaymentType("RECHARGE");
        paymentOrder.setAmount(request.getAmount());
        
        int result = paymentOrderMapper.insert(paymentOrder);
        if (result != 1) {
            throw new BusinessException("创建支付订单失败");
        }
        
        return createAlipayForm(paymentOrder, "余额充值");
    }

    @Override
    @Transactional
    public String createDepositOrder(Long userId) {
        BigDecimal depositAmount = new BigDecimal("200.00"); // 固定押金200元
        
        // 检查是否已缴纳押金
        User user = userMapper.selectById(userId);
        if (user.getDeposit().compareTo(BigDecimal.ZERO) > 0) {
            throw new BusinessException("您已缴纳押金，无需重复缴纳");
        }
        
        // 优先使用余额支付押金
        if (user.getBalance().compareTo(depositAmount) >= 0) {
            // 余额充足，直接从余额扣除押金
            User updateUser = new User();
            updateUser.setId(userId);
            updateUser.setBalance(user.getBalance().subtract(depositAmount));
            updateUser.setDeposit(depositAmount);
            
            int result = userMapper.updateById(updateUser);
            if (result != 1) {
                throw new BusinessException("押金缴纳失败，请稍后重试");
            }
            
            // 记录交易流水
            recordTransaction(userId, "DEPOSIT", depositAmount, null);
            
            log.info("用户{}成功使用余额缴纳押金{}元", userId, depositAmount);
            return "SUCCESS"; // 表示直接成功，无需跳转支付页面
            
        } else {
            // 余额不足，需要通过支付宝支付
            PaymentOrder paymentOrder = new PaymentOrder();
            paymentOrder.setPaymentOrderSn(generatePaymentOrderSn());
            paymentOrder.setUserId(userId);
            paymentOrder.setPaymentType("DEPOSIT");
            paymentOrder.setAmount(depositAmount);
            
            int result = paymentOrderMapper.insert(paymentOrder);
            if (result != 1) {
                throw new BusinessException("创建支付订单失败");
            }
            
            return createAlipayForm(paymentOrder, "押金缴纳");
        }
    }

    @Override
    @Transactional
    public void handleAlipayNotify(Map<String, String> params) {
        String outTradeNo = params.get("out_trade_no");
        String tradeNo = params.get("trade_no");
        String tradeStatus = params.get("trade_status");
        String totalAmount = params.get("total_amount");
        
        log.info("收到支付宝回调：订单号={}, 支付宝交易号={}, 状态={}, 金额={}", 
            outTradeNo, tradeNo, tradeStatus, totalAmount);
        
        try {
            // 验证签名
            if (!verifyAlipaySign(params)) {
                log.error("支付宝回调签名验证失败，可能存在安全风险，订单号: {}, 参数: {}", outTradeNo, params);
                return;
            }
            log.debug("支付宝回调签名验证成功，订单号: {}", outTradeNo);
            
            // 查询支付订单
            LambdaQueryWrapper<PaymentOrder> query = new LambdaQueryWrapper<>();
            query.eq(PaymentOrder::getPaymentOrderSn, outTradeNo);
            PaymentOrder paymentOrder = paymentOrderMapper.selectOne(query);
            
            if (paymentOrder == null) {
                log.error("支付回调处理失败：支付订单不存在，订单号: {}", outTradeNo);
                return;
            }
            
            if ("SUCCESS".equals(paymentOrder.getStatus())) {
                log.info("支付订单{}已处理过，跳过重复处理", outTradeNo);
                return;
            }
            
            log.info("开始处理支付订单：{}, 用户ID: {}, 支付类型: {}, 金额: {}", 
                outTradeNo, paymentOrder.getUserId(), paymentOrder.getPaymentType(), paymentOrder.getAmount());
            
            // 处理支付成功
            if ("TRADE_SUCCESS".equals(tradeStatus)) {
                // 更新支付订单状态
                PaymentOrder updateOrder = new PaymentOrder();
                updateOrder.setId(paymentOrder.getId());
                updateOrder.setStatus("SUCCESS");
                updateOrder.setAlipayTradeNo(tradeNo);
                updateOrder.setPayTime(LocalDateTime.now());
                paymentOrderMapper.updateById(updateOrder);
                
                log.info("支付订单{}状态已更新为成功", outTradeNo);
                
                // 更新用户余额或押金
                User user = userMapper.selectById(paymentOrder.getUserId());
                User updateUser = new User();
                updateUser.setId(paymentOrder.getUserId());
                
                if ("RECHARGE".equals(paymentOrder.getPaymentType())) {
                    // 充值
                    BigDecimal newBalance = user.getBalance().add(paymentOrder.getAmount());
                    updateUser.setBalance(newBalance);
                    recordTransaction(paymentOrder.getUserId(), "RECHARGE", 
                        paymentOrder.getAmount(), paymentOrder.getPaymentOrderSn());
                    
                    log.info("用户{}充值成功：订单{}, 充值金额: {}, 余额变化: {} -> {}", 
                        paymentOrder.getUserId(), outTradeNo, paymentOrder.getAmount(), 
                        user.getBalance(), newBalance);
                        
                } else if ("DEPOSIT".equals(paymentOrder.getPaymentType())) {
                    // 押金
                    updateUser.setDeposit(paymentOrder.getAmount());
                    recordTransaction(paymentOrder.getUserId(), "DEPOSIT", 
                        paymentOrder.getAmount(), paymentOrder.getPaymentOrderSn());
                    
                    log.info("用户{}押金缴纳成功：订单{}, 押金金额: {}, 原押金: {} -> 新押金: {}", 
                        paymentOrder.getUserId(), outTradeNo, paymentOrder.getAmount(), 
                        user.getDeposit(), paymentOrder.getAmount());
                }
                
                int updateResult = userMapper.updateById(updateUser);
                if (updateResult != 1) {
                    log.error("更新用户{}账户信息失败，支付订单: {}", paymentOrder.getUserId(), outTradeNo);
                    throw new BusinessException("账户更新失败");
                }
                
                log.info("支付处理完成：用户={}, 类型={}, 金额={}, 订单={}", 
                    paymentOrder.getUserId(), paymentOrder.getPaymentType(), 
                    paymentOrder.getAmount(), outTradeNo);
                    
            } else {
                log.warn("收到非成功状态的支付回调：订单{}, 状态: {}", outTradeNo, tradeStatus);
            }
            
        } catch (Exception e) {
            log.error("处理支付宝回调异常，订单号: {}, 错误信息: {}", outTradeNo, e.getMessage(), e);
        }
    }

    @Override
    public boolean verifyAlipaySign(Map<String, String> params) {
        try {
            return AlipaySignature.rsaCheckV1(params, alipayConfig.getAlipayPublicKey(), 
                alipayConfig.getCharset(), alipayConfig.getSignType());
        } catch (AlipayApiException e) {
            log.error("验证支付宝签名异常", e);
            return false;
        }
    }

    @Override
    public void recordTransaction(Long userId, String type, BigDecimal amount, String relatedOrderSn) {
        Transaction transaction = new Transaction();
        transaction.setUserId(userId);
        transaction.setType(type);
        transaction.setAmount(amount);
        transaction.setRelatedOrderSn(relatedOrderSn);
        
        transactionMapper.insert(transaction);
    }

    /**
     * 创建支付宝支付表单
     */
    private String createAlipayForm(PaymentOrder paymentOrder, String subject) {
        try {
            // 检查支付宝配置是否完整
            if (alipayConfig.getAppId() == null || alipayConfig.getAppId().contains("YOUR_") ||
                alipayConfig.getMerchantPrivateKey() == null || alipayConfig.getMerchantPrivateKey().contains("YOUR_") ||
                alipayConfig.getAlipayPublicKey() == null || alipayConfig.getAlipayPublicKey().contains("YOUR_")) {
                log.error("支付宝配置不完整，请在application.yml中配置正确的appId、merchantPrivateKey和alipayPublicKey");
                throw new BusinessException("支付配置不完整，请联系管理员");
            }
            
            AlipayClient alipayClient = new DefaultAlipayClient(
                alipayConfig.getGatewayUrl(),
                alipayConfig.getAppId(),
                alipayConfig.getMerchantPrivateKey(),
                alipayConfig.getFormat(),
                alipayConfig.getCharset(),
                alipayConfig.getAlipayPublicKey(),
                alipayConfig.getSignType()
            );
            
            AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
            request.setNotifyUrl(alipayConfig.getNotifyUrl());
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_trade_no", paymentOrder.getPaymentOrderSn());
            bizContent.put("total_amount", paymentOrder.getAmount().toString());
            bizContent.put("subject", subject);
            bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
            
            request.setBizContent(new ObjectMapper().writeValueAsString(bizContent));
            
            return alipayClient.pageExecute(request).getBody();
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建支付宝支付表单失败: {}", e.getMessage(), e);
            throw new BusinessException("创建支付表单失败: " + e.getMessage());
        }
    }

    /**
     * 生成支付订单号
     */
    @Override
    @Transactional
    public void refundDepositToBalance(Long userId) {
        // 1. 查询用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 2. 检查用户是否已缴纳押金
        if (user.getDeposit().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("您尚未缴纳押金，无法退押金");
        }
        
        // 3. 检查用户是否有进行中的租车订单
        LambdaQueryWrapper<Order> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.eq(Order::getUserId, userId)
                  .eq(Order::getStatus, OrderStatus.RENTING);
        Long activeOrderCount = orderMapper.selectCount(orderQuery);
        if (activeOrderCount > 0) {
            throw new BusinessException("您有正在进行的租车订单，无法退押金");
        }
        
        // 4. 将押金退到余额中
        BigDecimal depositAmount = user.getDeposit();
        
        // 更新用户信息：清零押金，增加余额
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setDeposit(BigDecimal.ZERO);
        updateUser.setBalance(user.getBalance().add(depositAmount));
        
        int result = userMapper.updateById(updateUser);
        if (result != 1) {
            throw new BusinessException("退押金失败，请稍后重试");
        }
        
        // 5. 记录交易流水
        recordTransaction(userId, "DEPOSIT_REFUND", depositAmount, null);
        
        log.info("用户{}成功退押金{}元到余额", userId, depositAmount);
    }

    private String generatePaymentOrderSn() {
        return "PAY" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }
}