package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.campus.vehicle.dto.LoginRequest;
import com.campus.vehicle.dto.RegisterRequest;
import com.campus.vehicle.dto.WithdrawRequest;
import com.campus.vehicle.entity.Transaction;
import com.campus.vehicle.entity.User;
import com.campus.vehicle.enums.UserStatus;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.TransactionMapper;
import com.campus.vehicle.mapper.UserMapper;
import com.campus.vehicle.service.UserService;
import com.campus.vehicle.utils.JwtUtil;
import com.campus.vehicle.vo.LoginResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private TransactionMapper transactionMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public void register(RegisterRequest request) {
        // 检查用户名是否已存在
        User existUser = findByUsername(request.getUsername());
        if (existUser != null) {
            throw new BusinessException("用户名已存在");
        }
        
        // 检查手机号是否已存在
        LambdaQueryWrapper<User> phoneQuery = new LambdaQueryWrapper<>();
        phoneQuery.eq(User::getPhone, request.getPhone());
        User existPhone = userMapper.selectOne(phoneQuery);
        if (existPhone != null) {
            throw new BusinessException("手机号已被注册");
        }
        
        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setNickname(request.getNickname());
        user.setPhone(request.getPhone());
        
        int result = userMapper.insert(user);
        if (result != 1) {
            throw new BusinessException("注册失败");
        }
    }

    @Override
    public LoginResponse login(LoginRequest request) {
        // 查找用户
        User user = findByUsername(request.getUsername());
        if (user == null) {
            throw new BusinessException(401, "用户名或密码错误");
        }
        
        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new BusinessException(401, "用户名或密码错误");
        }
        
        // 检查用户状态
        if (user.getStatus() != UserStatus.NORMAL) {
            throw new BusinessException(403, "账号已被冻结");
        }
        
        // 生成Token
        String token = jwtUtil.generateToken(user.getUsername(), user.getId());
        
        // 构建用户信息
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        BeanUtils.copyProperties(user, userInfo);
        
        return new LoginResponse(token, userInfo);
    }

    @Override
    public User findByUsername(String username) {
        LambdaQueryWrapper<User> query = new LambdaQueryWrapper<>();
        query.eq(User::getUsername, username);
        return userMapper.selectOne(query);
    }

    @Override
    public User findById(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    public LoginResponse.UserInfo getCurrentUserInfo() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BusinessException(401, "未登录");
        }
        
        String username = (String) authentication.getPrincipal();
        User user = findByUsername(username);
        if (user == null) {
            throw new BusinessException(401, "用户不存在");
        }
        
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        BeanUtils.copyProperties(user, userInfo);
        return userInfo;
    }

    @Override
    @Transactional
    public void withdraw(Long userId, WithdrawRequest request) {
        // 查找用户
        User user = findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查用户是否绑定手机号
        if (user.getPhone() == null || user.getPhone().trim().isEmpty()) {
            throw new BusinessException("请先绑定手机号");
        }
        
        // 检查余额是否充足
        if (user.getBalance().compareTo(request.getAmount()) < 0) {
            throw new BusinessException("余额不足");
        }
        
        // 扣除用户余额
        user.setBalance(user.getBalance().subtract(request.getAmount()));
        int updateResult = userMapper.updateById(user);
        if (updateResult != 1) {
            throw new BusinessException("提现失败，请重试");
        }
        
        // 创建提现交易记录
        Transaction transaction = new Transaction();
        transaction.setUserId(userId);
        transaction.setType("WITHDRAW");
        transaction.setAmount(request.getAmount());
        transaction.setRelatedOrderSn("WITHDRAW_" + System.currentTimeMillis());
        
        int insertResult = transactionMapper.insert(transaction);
        if (insertResult != 1) {
            throw new BusinessException("提现失败，请重试");
        }
    }
}