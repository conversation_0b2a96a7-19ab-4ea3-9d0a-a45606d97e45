package com.campus.vehicle.service;

import com.campus.vehicle.dto.RechargeRequest;
import com.campus.vehicle.entity.PaymentOrder;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Map;

public interface PaymentService {
    
    /**
     * 用户充值
     */
    String createRechargeOrder(Long userId, RechargeRequest request);
    
    /**
     * 用户缴纳押金
     */
    String createDepositOrder(Long userId);
    
    /**
     * 用户退押金（退到余额）
     */
    void refundDepositToBalance(Long userId);
    
    /**
     * 支付宝异步回调处理
     */
    void handleAlipayNotify(Map<String, String> params);
    
    /**
     * 验证支付宝签名
     */
    boolean verifyAlipaySign(Map<String, String> params);
    
    /**
     * 记录交易流水
     */
    void recordTransaction(Long userId, String type, BigDecimal amount, String relatedOrderSn);
}