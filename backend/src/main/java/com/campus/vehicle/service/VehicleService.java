package com.campus.vehicle.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.AddVehicleRequest;
import com.campus.vehicle.entity.Vehicle;
import com.campus.vehicle.enums.VehicleStatus;
import com.campus.vehicle.vo.VehicleStatusResponse;

import java.util.List;

public interface VehicleService {
    
    /**
     * 获取所有车辆状态 (仿真轮询接口)
     */
    List<VehicleStatusResponse> getAllVehicleStatus();
    
    /**
     * 管理员投放新车
     */
    void addVehicle(AddVehicleRequest request);
    
    /**
     * 分页获取车辆列表
     */
    Page<Vehicle> getVehicleList(int current, int size);
    
    /**
     * 根据ID获取车辆
     */
    Vehicle getById(Long id);
    
    /**
     * 更新车辆状态
     */
    void updateVehicleStatus(Long vehicleId, VehicleStatus status);
    
    /**
     * 更新车辆位置和电量
     */
    void updateVehiclePosition(Vehicle vehicle);
}