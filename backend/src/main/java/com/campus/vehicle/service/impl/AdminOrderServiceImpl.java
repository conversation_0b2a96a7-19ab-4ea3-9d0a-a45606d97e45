package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.entity.Order;
import com.campus.vehicle.enums.OrderStatus;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.OrderMapper;
import com.campus.vehicle.service.AdminOrderService;
import com.campus.vehicle.vo.AdminOrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AdminOrderServiceImpl implements AdminOrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Override
    public Page<AdminOrderVO> getOrderList(int current, int size, String status, String keyword) {
        if (current <= 0 || size <= 0) {
            throw new BusinessException("分页参数无效");
        }
        
        try {
            Page<AdminOrderVO> result = new Page<>(current, size);
            
            // 使用自定义查询避免N+1问题
            long total = getOrderCountWithConditions(status, keyword);
            result.setTotal(total);
            result.setPages((total + size - 1) / size);
            
            if (total == 0) {
                return result;
            }
            
            long offset = (current - 1) * size;
            List<AdminOrderVO> records = getOrderListWithJoin(offset, size, status, keyword);
            result.setRecords(records);
            
            return result;
        } catch (Exception e) {
            throw new BusinessException("查询订单列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 使用JOIN查询订单列表，避免N+1问题
     */
    private List<AdminOrderVO> getOrderListWithJoin(long offset, int size, String status, String keyword) {
        // 验证状态参数
        String validStatus = null;
        if (status != null && !status.trim().isEmpty()) {
            try {
                OrderStatus.valueOf(status.toUpperCase());
                validStatus = status;
            } catch (IllegalArgumentException e) {
                // 忽略无效状态
            }
        }
        
        return orderMapper.selectOrdersWithUserAndVehicle(validStatus, keyword, offset, size);
    }
    
    /**
     * 获取符合条件的订单总数
     */
    private long getOrderCountWithConditions(String status, String keyword) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        
        // 状态筛选
        if (status != null && !status.trim().isEmpty()) {
            try {
                OrderStatus orderStatus = OrderStatus.valueOf(status.toUpperCase());
                queryWrapper.eq("status", orderStatus);
            } catch (IllegalArgumentException e) {
                // 忽略无效状态
            }
        }
        
        // 关键词搜索
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like("order_sn", keyword);
        }
        
        return orderMapper.selectCount(queryWrapper);
    }
}