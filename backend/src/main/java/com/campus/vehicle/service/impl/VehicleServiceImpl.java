package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.AddVehicleRequest;
import com.campus.vehicle.entity.Vehicle;
import com.campus.vehicle.enums.VehicleStatus;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.VehicleMapper;
import com.campus.vehicle.service.VehicleService;
import com.campus.vehicle.vo.VehicleStatusResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class VehicleServiceImpl implements VehicleService {

    @Autowired
    private VehicleMapper vehicleMapper;

    @Override
    public List<VehicleStatusResponse> getAllVehicleStatus() {
        LambdaQueryWrapper<Vehicle> query = new LambdaQueryWrapper<>();
        query.select(Vehicle::getId, Vehicle::getVehicleSn, Vehicle::getStatus, 
                    Vehicle::getBattery, Vehicle::getPositionX, Vehicle::getPositionY);
        List<Vehicle> vehicles = vehicleMapper.selectList(query);
        
        return vehicles.stream().map(vehicle -> {
            VehicleStatusResponse response = new VehicleStatusResponse();
            BeanUtils.copyProperties(vehicle, response);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public void addVehicle(AddVehicleRequest request) {
        Vehicle vehicle = new Vehicle();
        vehicle.setVehicleSn(generateVehicleSn());
        vehicle.setPositionX(request.getPositionX());
        vehicle.setPositionY(request.getPositionY());
        vehicle.setBattery(request.getBattery());
        vehicle.setLastMaintainTime(LocalDateTime.now());
        
        int result = vehicleMapper.insert(vehicle);
        if (result != 1) {
            throw new BusinessException("添加车辆失败");
        }
    }

    @Override
    public Page<Vehicle> getVehicleList(int current, int size) {
        Page<Vehicle> page = new Page<>(current, size);
        return vehicleMapper.selectPage(page, null);
    }

    @Override
    public Vehicle getById(Long id) {
        Vehicle vehicle = vehicleMapper.selectById(id);
        if (vehicle == null) {
            throw new BusinessException("车辆不存在");
        }
        return vehicle;
    }

    @Override
    public void updateVehicleStatus(Long vehicleId, VehicleStatus status) {
        Vehicle vehicle = new Vehicle();
        vehicle.setId(vehicleId);
        vehicle.setStatus(status);
        
        int result = vehicleMapper.updateById(vehicle);
        if (result != 1) {
            throw new BusinessException("更新车辆状态失败");
        }
    }

    @Override
    public void updateVehiclePosition(Vehicle vehicle) {
        int result = vehicleMapper.updateById(vehicle);
        if (result != 1) {
            throw new BusinessException("更新车辆位置失败");  
        }
    }

    /**
     * 生成车辆编号
     */
    private String generateVehicleSn() {
        return "EV" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }
}