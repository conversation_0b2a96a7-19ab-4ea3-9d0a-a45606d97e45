package com.campus.vehicle.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.entity.Coupon;

import java.util.List;

public interface AdminCouponService {
    
    /**
     * 获取优惠券模板列表
     */
    Page<Coupon> getCouponList(int current, int size);
    
    /**
     * 创建优惠券模板
     */
    void createCoupon(Coupon coupon);
    
    /**
     * 更新优惠券模板
     */
    void updateCoupon(Coupon coupon);
    
    /**
     * 删除优惠券模板
     */
    void deleteCoupon(Long id);
    
    /**
     * 批量发放优惠券
     */
    void batchIssueCoupon(List<Long> userIds, Long couponId);
}