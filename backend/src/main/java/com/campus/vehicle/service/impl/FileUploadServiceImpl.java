package com.campus.vehicle.service.impl;

import com.campus.vehicle.entity.User;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.UserMapper;
import com.campus.vehicle.service.FileUploadService;
import com.campus.vehicle.utils.FileUploadUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
public class FileUploadServiceImpl implements FileUploadService {

    @Autowired
    private FileUploadUtil fileUploadUtil;
    
    @Autowired
    private UserMapper userMapper;

    @Override
    @Transactional
    public String uploadAvatar(Long userId, MultipartFile file) {
        // 上传文件
        String avatarUrl = fileUploadUtil.uploadFile(file, "avatars");
        
        // 更新用户头像
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        user.setAvatarUrl(avatarUrl);
        int result = userMapper.updateById(user);
        if (result != 1) {
            throw new BusinessException("头像更新失败");
        }
        
        return avatarUrl;
    }

    @Override
    public String uploadImage(MultipartFile file) {
        return fileUploadUtil.uploadFile(file, "images");
    }
}