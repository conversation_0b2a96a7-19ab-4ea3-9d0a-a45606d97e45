package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.campus.vehicle.constant.BusinessConstants;
import com.campus.vehicle.entity.*;
import com.campus.vehicle.enums.FaultReportStatus;
import com.campus.vehicle.enums.OrderStatus;
import com.campus.vehicle.enums.VehicleStatus;
import com.campus.vehicle.mapper.*;
import com.campus.vehicle.service.AdminDashboardService;
import com.campus.vehicle.vo.DashboardStatsResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Service
public class AdminDashboardServiceImpl implements AdminDashboardService {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private VehicleMapper vehicleMapper;
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private FaultReportMapper faultReportMapper;
    
    @Autowired
    private TransactionMapper transactionMapper;

    @Override
    public DashboardStatsResponse getDashboardStats() {
        DashboardStatsResponse response = new DashboardStatsResponse();
        
        // 用户总数（排除已删除用户）
        QueryWrapper<User> userQuery = new QueryWrapper<>();
        userQuery.eq("role", BusinessConstants.UserRole.USER);
        response.setTotalUsers(userMapper.selectCount(userQuery));
        
        // 车辆总数
        response.setTotalVehicles(vehicleMapper.selectCount(null));
        
        // 当前租赁订单数
        QueryWrapper<Order> rentingOrderQuery = new QueryWrapper<>();
        rentingOrderQuery.eq("status", OrderStatus.RENTING);
        response.setCurrentRentingOrders(orderMapper.selectCount(rentingOrderQuery));
        
        // 故障车辆数
        QueryWrapper<Vehicle> faultVehicleQuery = new QueryWrapper<>();
        faultVehicleQuery.eq("status", VehicleStatus.FAULTY);
        response.setFaultVehicles(vehicleMapper.selectCount(faultVehicleQuery));
        
        // 可用车辆数
        QueryWrapper<Vehicle> availableVehicleQuery = new QueryWrapper<>();
        availableVehicleQuery.eq("status", VehicleStatus.AVAILABLE);
        response.setAvailableVehicles(vehicleMapper.selectCount(availableVehicleQuery));
        
        // 待处理故障报告数
        QueryWrapper<FaultReport> pendingFaultQuery = new QueryWrapper<>();
        pendingFaultQuery.eq("status", FaultReportStatus.PENDING);
        response.setPendingFaultReports(faultReportMapper.selectCount(pendingFaultQuery));
        
        // 今日收入（租车支付）
        LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        
        QueryWrapper<Transaction> todayRevenueQuery = new QueryWrapper<>();
        todayRevenueQuery.eq("type", BusinessConstants.TransactionType.RENT_PAYMENT)
                        .between("create_time", todayStart, todayEnd);
        
        BigDecimal todayRevenue = transactionMapper.selectList(todayRevenueQuery)
                .stream()
                .map(Transaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        response.setTodayRevenue(todayRevenue);
        
        return response;
    }
}