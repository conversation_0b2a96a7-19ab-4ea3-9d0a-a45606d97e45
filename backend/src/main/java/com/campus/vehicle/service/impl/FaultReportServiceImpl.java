package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.FaultReportRequest;
import com.campus.vehicle.entity.FaultReport;
import com.campus.vehicle.enums.FaultReportStatus;
import com.campus.vehicle.enums.VehicleStatus;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.FaultReportMapper;
import com.campus.vehicle.service.FaultReportService;
import com.campus.vehicle.service.VehicleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class FaultReportServiceImpl implements FaultReportService {

    @Autowired
    private FaultReportMapper faultReportMapper;
    
    @Autowired
    private VehicleService vehicleService;

    @Override
    @Transactional
    public void submitFaultReport(Long userId, FaultReportRequest request) {
        // 验证车辆存在
        vehicleService.getById(request.getVehicleId());
        
        // 创建故障报告
        FaultReport faultReport = new FaultReport();
        faultReport.setUserId(userId);
        faultReport.setVehicleId(request.getVehicleId());
        faultReport.setFaultType(request.getFaultType());
        faultReport.setDescription(request.getDescription());
        faultReport.setImageUrls(request.getImageUrls());
        
        int result = faultReportMapper.insert(faultReport);
        if (result != 1) {
            throw new BusinessException("提交故障报告失败");
        }
        
        // 将车辆状态设置为故障
        vehicleService.updateVehicleStatus(request.getVehicleId(), VehicleStatus.FAULTY);
    }

    @Override
    public Page<FaultReport> getFaultReportList(int current, int size, String status) {
        Page<FaultReport> page = new Page<>(current, size);
        LambdaQueryWrapper<FaultReport> query = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(status)) {
            query.eq(FaultReport::getStatus, status);
        }
        
        query.orderByDesc(FaultReport::getReportTime);
        return faultReportMapper.selectPage(page, query);
    }

    @Override
    @Transactional
    public void updateFaultReportStatus(Long id, FaultReportStatus status) {
        FaultReport faultReport = faultReportMapper.selectById(id);
        if (faultReport == null) {
            throw new BusinessException("故障报告不存在");
        }
        
        FaultReport updateReport = new FaultReport();
        updateReport.setId(id);
        updateReport.setStatus(status);
        
        if (status == FaultReportStatus.RESOLVED) {
            updateReport.setResolveTime(LocalDateTime.now());
            // 将车辆状态恢复为可用
            vehicleService.updateVehicleStatus(faultReport.getVehicleId(), VehicleStatus.AVAILABLE);
        }
        
        int result = faultReportMapper.updateById(updateReport);
        if (result != 1) {
            throw new BusinessException("更新故障报告状态失败");
        }
    }

    @Override
    public FaultReport getById(Long id) {
        FaultReport faultReport = faultReportMapper.selectById(id);
        if (faultReport == null) {
            throw new BusinessException("故障报告不存在");
        }
        return faultReport;
    }
}