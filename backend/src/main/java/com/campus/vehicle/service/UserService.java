package com.campus.vehicle.service;

import com.campus.vehicle.dto.LoginRequest;
import com.campus.vehicle.dto.RegisterRequest;
import com.campus.vehicle.dto.WithdrawRequest;
import com.campus.vehicle.entity.User;
import com.campus.vehicle.vo.LoginResponse;

public interface UserService {
    
    /**
     * 用户注册
     */
    void register(RegisterRequest request);
    
    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest request);
    
    /**
     * 根据用户名查找用户
     */
    User findByUsername(String username);
    
    /**
     * 根据ID查找用户
     */
    User findById(Long id);
    
    /**
     * 获取当前登录用户信息
     */
    LoginResponse.UserInfo getCurrentUserInfo();
    
    /**
     * 用户提现
     */
    void withdraw(Long userId, WithdrawRequest request);
}