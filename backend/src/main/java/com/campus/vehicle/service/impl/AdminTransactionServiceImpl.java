package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.entity.Transaction;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.TransactionMapper;
import com.campus.vehicle.service.AdminTransactionService;
import com.campus.vehicle.vo.AdminTransactionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AdminTransactionServiceImpl implements AdminTransactionService {

    @Autowired
    private TransactionMapper transactionMapper;

    @Override
    public Page<AdminTransactionVO> getTransactionList(int current, int size, String type, String keyword) {
        if (current <= 0 || size <= 0) {
            throw new BusinessException("分页参数无效");
        }
        
        try {
            Page<AdminTransactionVO> result = new Page<>(current, size);
            
            // 获取总数
            long total = getTransactionCountWithConditions(type, keyword);
            result.setTotal(total);
            result.setPages((total + size - 1) / size);
            
            if (total == 0) {
                return result;
            }
            
            // 使用JOIN查询避免N+1问题
            long offset = (current - 1) * size;
            List<AdminTransactionVO> records = getTransactionListWithJoin(offset, size, type, keyword);
            result.setRecords(records);
            
            return result;
        } catch (Exception e) {
            throw new BusinessException("查询交易列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 使用JOIN查询交易列表，避免N+1问题
     */
    private List<AdminTransactionVO> getTransactionListWithJoin(long offset, int size, String type, String keyword) {
        return transactionMapper.selectTransactionsWithUser(type, keyword, offset, size);
    }
    
    /**
     * 获取符合条件的交易总数
     */
    private long getTransactionCountWithConditions(String type, String keyword) {
        QueryWrapper<Transaction> queryWrapper = new QueryWrapper<>();
        
        // 根据交易类型筛选
        if (type != null && !type.trim().isEmpty()) {
            queryWrapper.eq("type", type);
        }
        
        // 根据关键词搜索（订单号）
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like("related_order_sn", keyword);
        }
        
        return transactionMapper.selectCount(queryWrapper);
    }
}