package com.campus.vehicle.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.FaultReportRequest;
import com.campus.vehicle.entity.FaultReport;
import com.campus.vehicle.enums.FaultReportStatus;

public interface FaultReportService {
    
    /**
     * 用户提交故障报告
     */
    void submitFaultReport(Long userId, FaultReportRequest request);
    
    /**
     * 分页获取故障报告列表
     */
    Page<FaultReport> getFaultReportList(int current, int size, String status);
    
    /**
     * 更新故障报告状态
     */
    void updateFaultReportStatus(Long id, FaultReportStatus status);
    
    /**
     * 根据ID获取故障报告
     */
    FaultReport getById(Long id);
}