package com.campus.vehicle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.EndRentRequest;
import com.campus.vehicle.dto.StartRentRequest;
import com.campus.vehicle.entity.BillingRule;
import com.campus.vehicle.entity.Order;
import com.campus.vehicle.entity.User;
import com.campus.vehicle.entity.Vehicle;
import com.campus.vehicle.enums.OrderStatus;
import com.campus.vehicle.enums.UserStatus;
import com.campus.vehicle.enums.VehicleStatus;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.mapper.BillingRuleMapper;
import com.campus.vehicle.mapper.OrderMapper;
import com.campus.vehicle.mapper.UserMapper;
import com.campus.vehicle.service.CouponService;
import com.campus.vehicle.service.RentService;
import com.campus.vehicle.service.VehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.UUID;

@Slf4j
@Service
public class RentServiceImpl implements RentService {

    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private VehicleService vehicleService;
    
    @Autowired
    private BillingRuleMapper billingRuleMapper;
    
    @Autowired
    private CouponService couponService;

    @Override
    @Transactional
    public Order startRent(Long userId, StartRentRequest request) {
        log.info("用户{}开始租车流程，车辆ID: {}", userId, request.getVehicleId());
        
        // 检查用户状态
        User user = userMapper.selectById(userId);
        if (user == null) {
            log.error("租车失败：用户{}不存在", userId);
            throw new BusinessException("用户不存在");
        }
        if (user.getStatus() != UserStatus.NORMAL) {
            log.warn("租车失败：用户{}状态异常，当前状态: {}", userId, user.getStatus());
            throw new BusinessException("账号已被冻结，无法租车");
        }
        
        // 检查押金
        if (user.getDeposit().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("租车失败：用户{}未缴纳押金，当前押金: {}", userId, user.getDeposit());
            throw new BusinessException("请先缴纳押金");
        }
        
        // 检查是否有进行中的订单
        Order currentOrder = getCurrentRentOrder(userId);
        if (currentOrder != null) {
            log.warn("租车失败：用户{}已有进行中的订单: {}", userId, currentOrder.getId());
            throw new BusinessException("您有正在进行的租车订单，请先结束");
        }
        
        // 检查车辆状态
        Vehicle vehicle = vehicleService.getById(request.getVehicleId());
        if (vehicle.getStatus() != VehicleStatus.AVAILABLE) {
            log.warn("租车失败：车辆{}状态不可用，当前状态: {}", request.getVehicleId(), vehicle.getStatus());
            throw new BusinessException("车辆不可用");
        }
        
        // 创建订单
        Order order = new Order();
        order.setOrderSn(generateOrderSn());
        order.setUserId(userId);
        order.setVehicleId(request.getVehicleId());
        order.setStartTime(LocalDateTime.now());
        
        int result = orderMapper.insert(order);
        if (result != 1) {
            log.error("租车失败：创建订单失败，用户: {}, 车辆: {}", userId, request.getVehicleId());
            throw new BusinessException("创建订单失败");
        }
        
        // 更新车辆状态
        vehicleService.updateVehicleStatus(request.getVehicleId(), VehicleStatus.RENTED);
        
        log.info("租车成功：用户{}成功租用车辆{}，订单号: {}, 订单ID: {}", 
            userId, request.getVehicleId(), order.getOrderSn(), order.getId());
        
        return order;
    }

    @Override
    @Transactional
    public void endRent(Long userId, EndRentRequest request) {
        log.info("用户{}开始还车流程，订单ID: {}, 优惠券ID: {}", userId, request.getOrderId(), request.getUserCouponId());
        
        // 获取订单
        Order order = orderMapper.selectById(request.getOrderId());
        if (order == null) {
            log.error("还车失败：订单{}不存在", request.getOrderId());
            throw new BusinessException("订单不存在");
        }
        
        if (!order.getUserId().equals(userId)) {
            log.error("还车失败：用户{}无权操作订单{}", userId, request.getOrderId());
            throw new BusinessException("无权操作此订单");
        }
        
        if (order.getStatus() != OrderStatus.RENTING) {
            log.error("还车失败：订单{}状态异常，当前状态: {}", request.getOrderId(), order.getStatus());
            throw new BusinessException("订单状态异常");
        }
        
        // 计算费用
        LocalDateTime endTime = LocalDateTime.now();
        Duration duration = Duration.between(order.getStartTime(), endTime);
        int minutes = (int) duration.toMinutes();
        if (minutes < 1) {
            minutes = 1; // 最少1分钟
        }

        BigDecimal totalFee = calculateFee(order);
        BigDecimal discount = BigDecimal.ZERO;
        Long finalUserCouponId = null;
        
        log.info("还车费用计算：订单{}, 使用时长{}分钟, 基础费用: {}", 
            request.getOrderId(), minutes, totalFee);
        
        // 处理优惠券
        if (request.getUserCouponId() != null) {
            log.info("用户{}尝试使用优惠券: {}", userId, request.getUserCouponId());
            if (couponService.isCouponUsable(request.getUserCouponId(), totalFee)) {
                discount = couponService.calculateDiscount(request.getUserCouponId(), totalFee);
                finalUserCouponId = request.getUserCouponId();
                log.info("优惠券{}生效，折扣金额: {}", request.getUserCouponId(), discount);
            } else {
                log.warn("优惠券{}不可用或不满足使用条件", request.getUserCouponId());
            }
        }
        
        BigDecimal finalFee = totalFee.subtract(discount);
        if (finalFee.compareTo(BigDecimal.ZERO) < 0) {
            finalFee = BigDecimal.ZERO;
        }
        
        log.info("最终费用计算：订单{}, 原价: {}, 折扣: {}, 实付: {}", 
            request.getOrderId(), totalFee, discount, finalFee);
        
        // 检查余额
        User user = userMapper.selectById(userId);
        if (user.getBalance().compareTo(finalFee) < 0) {
            log.error("还车失败：用户{}余额不足，当前余额: {}, 需要支付: {}", 
                userId, user.getBalance(), finalFee);
            throw new BusinessException("余额不足，请充值后再结束租车");
        }
        
        // 扣费
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setBalance(user.getBalance().subtract(finalFee));
        userMapper.updateById(updateUser);
        
        log.info("用户{}扣费成功，扣费金额: {}, 余额变化: {} -> {}", 
            userId, finalFee, user.getBalance(), updateUser.getBalance());
        
        // 使用优惠券
        if (finalUserCouponId != null) {
            couponService.useCoupon(finalUserCouponId);
            log.info("优惠券{}已使用", finalUserCouponId);
        }
        
        // 更新订单
        order.setEndTime(endTime);
        order.setDuration(minutes);
        order.setBaseFee(totalFee);
        order.setDiscount(discount);
        order.setFinalFee(finalFee);
        order.setUserCouponId(finalUserCouponId);
        order.setStatus(OrderStatus.COMPLETED);
        orderMapper.updateById(order);
        
        // 更新车辆状态
        vehicleService.updateVehicleStatus(order.getVehicleId(), VehicleStatus.AVAILABLE);
        
        log.info("还车成功：用户{}完成订单{}，车辆{}已释放，总时长{}分钟，实付费用: {}", 
            userId, request.getOrderId(), order.getVehicleId(), minutes, finalFee);
    }

    @Override
    public BigDecimal calculateFee(Order order) {
        // 获取计费规则
        LambdaQueryWrapper<BillingRule> query = new LambdaQueryWrapper<>();
        query.eq(BillingRule::getIsActive, true);
        BillingRule rule = billingRuleMapper.selectOne(query);
        
        if (rule == null) {
            // 默认计费规则：15分钟2元，超出每5分钟1元
            rule = new BillingRule();
            rule.setBaseDuration(15);
            rule.setBaseFee(new BigDecimal("2.00"));
            rule.setExtraDurationUnit(5);
            rule.setExtraFeeUnit(new BigDecimal("1.00"));
        }
        
        // 计算实际时长
        Duration duration = Duration.between(order.getStartTime(), LocalDateTime.now());
        int minutes = (int) duration.toMinutes();
        if (minutes < 1) {
            minutes = 1;
        }
        
        BigDecimal totalFee = rule.getBaseFee();
        
        // 计算超时费用
        if (minutes > rule.getBaseDuration()) {
            int extraMinutes = minutes - rule.getBaseDuration();
            int extraUnits = (int) Math.ceil((double) extraMinutes / rule.getExtraDurationUnit());
            BigDecimal extraFee = rule.getExtraFeeUnit().multiply(new BigDecimal(extraUnits));
            totalFee = totalFee.add(extraFee);
        }
        
        return totalFee.setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public Order getCurrentRentOrder(Long userId) {
        LambdaQueryWrapper<Order> query = new LambdaQueryWrapper<>();
        query.eq(Order::getUserId, userId)
             .eq(Order::getStatus, OrderStatus.RENTING);
        return orderMapper.selectOne(query);
    }

    /**
     * 生成订单号
     */
    private String generateOrderSn() {
        return "R" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 6).toUpperCase();
    }

    @Override
    public Page<Order> getUserTrips(Long userId, int current, int size) {
        Page<Order> page = new Page<>(current, size);
        LambdaQueryWrapper<Order> query = new LambdaQueryWrapper<>();
        query.eq(Order::getUserId, userId)
             .orderByDesc(Order::getCreateTime);
        return orderMapper.selectPage(page, query);
    }
}