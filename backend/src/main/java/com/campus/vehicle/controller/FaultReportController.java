package com.campus.vehicle.controller;

import com.campus.vehicle.dto.FaultReportRequest;
import com.campus.vehicle.service.FaultReportService;
import com.campus.vehicle.utils.JwtUtil;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/fault-reports")
public class FaultReportController {

    @Autowired
    private FaultReportService faultReportService;
    
    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 用户提交故障报告
     */
    @PostMapping
    public Result<String> submitFaultReport(@RequestBody @Validated FaultReportRequest request,
                                          HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        faultReportService.submitFaultReport(userId, request);
        return Result.success("故障报告提交成功，我们将尽快处理");
    }

    /**
     * 从Token中获取用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            return jwtUtil.getUserIdFromToken(token);
        }
        throw new RuntimeException("无法获取用户ID");
    }
}