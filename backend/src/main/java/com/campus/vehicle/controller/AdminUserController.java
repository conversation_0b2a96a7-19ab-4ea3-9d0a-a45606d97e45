package com.campus.vehicle.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.UpdateUserStatusRequest;
import com.campus.vehicle.entity.User;
import com.campus.vehicle.service.AdminUserService;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.campus.vehicle.annotation.RequireAdmin;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/users")
public class AdminUserController {

    @Autowired
    private AdminUserService adminUserService;

    /**
     * 分页查询所有用户列表
     */
    @GetMapping
    @RequireAdmin
    public Result<Page<User>> getUserList(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword) {
        Page<User> userPage = adminUserService.getUserList(current, size, keyword);
        return Result.success(userPage);
    }

    /**
     * 更新用户状态（冻结/解冻）
     */
    @PutMapping("/{id}/status")
    @RequireAdmin
    public Result<String> updateUserStatus(
            @PathVariable Long id,
            @RequestBody @Validated UpdateUserStatusRequest request) {
        adminUserService.updateUserStatus(id, request.getStatus());
        return Result.success("用户状态更新成功");
    }
}