package com.campus.vehicle.controller;

import com.campus.vehicle.dto.RechargeRequest;
import com.campus.vehicle.service.PaymentService;
import com.campus.vehicle.utils.JwtUtil;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/payment")
public class PaymentController {

    @Autowired
    private PaymentService paymentService;
    
    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 用户充值
     */
    @PostMapping("/recharge")
    public void recharge(@RequestBody @Validated RechargeRequest request,
                         HttpServletRequest httpRequest,
                         HttpServletResponse response) throws IOException {
        Long userId = getCurrentUserId(httpRequest);
        String form = paymentService.createRechargeOrder(userId, request);
        
        response.setContentType("text/html;charset=utf-8");
        response.getWriter().write(form);
        response.getWriter().flush();
    }

    /**
     * 用户缴纳押金
     */
    @PostMapping("/deposit")
    public void deposit(HttpServletRequest httpRequest,
                        HttpServletResponse response) throws IOException {
        Long userId = getCurrentUserId(httpRequest);
        String result = paymentService.createDepositOrder(userId);
        
        if ("SUCCESS".equals(result)) {
            // 余额支付成功，返回JSON响应
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write("{\"code\":200,\"message\":\"押金缴纳成功\",\"data\":null}");
        } else {
            // 需要跳转支付宝，返回HTML表单
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write(result);
        }
        response.getWriter().flush();
    }

    /**
     * 用户退押金（退到余额）
     */
    @PostMapping("/refund-deposit")
    public Result<String> refundDeposit(HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        paymentService.refundDepositToBalance(userId);
        return Result.success("押金已成功退到您的账户余额中");
    }

    /**
     * 支付宝异步回调
     */
    @PostMapping("/alipay/notify")
    public String alipayNotify(HttpServletRequest request) {
        try {
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();
            
            for (String key : requestParams.keySet()) {
                String[] values = requestParams.get(key);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                params.put(key, valueStr);
            }
            
            paymentService.handleAlipayNotify(params);
            return "success";
            
        } catch (Exception e) {
            return "failure";
        }
    }

    /**
     * 从Token中获取用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            return jwtUtil.getUserIdFromToken(token);
        }
        throw new RuntimeException("无法获取用户ID");
    }
}