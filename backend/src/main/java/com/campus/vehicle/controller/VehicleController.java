package com.campus.vehicle.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.AddVehicleRequest;
import com.campus.vehicle.entity.Vehicle;
import com.campus.vehicle.service.VehicleService;
import com.campus.vehicle.vo.Result;
import com.campus.vehicle.vo.VehicleStatusResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/vehicles")
public class VehicleController {

    @Autowired
    private VehicleService vehicleService;

    /**
     * 获取所有车辆状态 (核心轮询接口)
     */
    @GetMapping("/status")
    public Result<List<VehicleStatusResponse>> getAllVehicleStatus() {
        List<VehicleStatusResponse> vehicles = vehicleService.getAllVehicleStatus();
        return Result.success(vehicles);
    }
}