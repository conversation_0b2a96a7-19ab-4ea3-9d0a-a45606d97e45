package com.campus.vehicle.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.service.AdminOrderService;
import com.campus.vehicle.vo.AdminOrderVO;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.campus.vehicle.annotation.RequireAdmin;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/orders")
public class AdminOrderController {

    @Autowired
    private AdminOrderService adminOrderService;

    /**
     * 分页查询所有订单列表
     */
    @GetMapping
    @RequireAdmin
    public Result<Page<AdminOrderVO>> getOrderList(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword) {
        Page<AdminOrderVO> orderPage = adminOrderService.getOrderList(current, size, status, keyword);
        return Result.success(orderPage);
    }
}