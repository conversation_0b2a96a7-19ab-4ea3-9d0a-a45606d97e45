package com.campus.vehicle.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.entity.Order;
import com.campus.vehicle.dto.WithdrawRequest;
import com.campus.vehicle.service.CouponService;
import com.campus.vehicle.service.RentService;
import com.campus.vehicle.service.UserService;
import com.campus.vehicle.utils.JwtUtil;
import com.campus.vehicle.vo.LoginResponse;
import com.campus.vehicle.vo.Result;
import com.campus.vehicle.vo.UserCouponVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;
    
    @Autowired
    private CouponService couponService;
    
    @Autowired
    private RentService rentService;
    
    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/info")
    public Result<LoginResponse.UserInfo> getCurrentUserInfo() {
        LoginResponse.UserInfo userInfo = userService.getCurrentUserInfo();
        return Result.success(userInfo);
    }

    /**
     * 获取我的优惠券
     */
    @GetMapping("/coupons")
    public Result<List<UserCouponVO>> getUserCoupons(HttpServletRequest request) {
        Long userId = getCurrentUserId(request);
        List<UserCouponVO> coupons = couponService.getUserCoupons(userId);
        return Result.success(coupons);
    }

    /**
     * 获取我的历史订单
     */
    @GetMapping("/trips")
    public Result<Page<Order>> getUserTrips(HttpServletRequest request,
                                           @RequestParam(defaultValue = "1") int current,
                                           @RequestParam(defaultValue = "10") int size) {
        Long userId = getCurrentUserId(request);
        Page<Order> trips = rentService.getUserTrips(userId, current, size);
        return Result.success(trips);
    }

    /**
     * 用户提现
     */
    @PostMapping("/withdraw")
    public Result<String> withdraw(@RequestBody WithdrawRequest request, HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        userService.withdraw(userId, request);
        return Result.success("提现成功");
    }

    /**
     * 从Token中获取用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            return jwtUtil.getUserIdFromToken(token);
        }
        throw new RuntimeException("无法获取用户ID");
    }
}