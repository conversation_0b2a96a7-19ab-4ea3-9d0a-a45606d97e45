package com.campus.vehicle.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.service.AdminTransactionService;
import com.campus.vehicle.vo.AdminTransactionVO;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.campus.vehicle.annotation.RequireAdmin;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/transactions")
public class AdminTransactionController {

    @Autowired
    private AdminTransactionService adminTransactionService;

    /**
     * 分页查询所有交易流水
     */
    @GetMapping
    @RequireAdmin
    public Result<Page<AdminTransactionVO>> getTransactionList(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String keyword) {
        Page<AdminTransactionVO> transactionPage = adminTransactionService.getTransactionList(current, size, type, keyword);
        return Result.success(transactionPage);
    }
}