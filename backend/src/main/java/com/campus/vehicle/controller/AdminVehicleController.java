package com.campus.vehicle.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.AddVehicleRequest;
import com.campus.vehicle.entity.Vehicle;
import com.campus.vehicle.service.VehicleService;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.campus.vehicle.annotation.RequireAdmin;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/vehicles")
public class AdminVehicleController {

    @Autowired
    private VehicleService vehicleService;

    /**
     * 管理员投放新车
     */
    @PostMapping
    @RequireAdmin
    public Result<String> addVehicle(@RequestBody @Validated AddVehicleRequest request) {
        vehicleService.addVehicle(request);
        return Result.success("投放车辆成功");
    }

    /**
     * 分页获取车辆列表
     */
    @GetMapping
    @RequireAdmin
    public Result<Page<Vehicle>> getVehicleList(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        Page<Vehicle> vehiclePage = vehicleService.getVehicleList(current, size);
        return Result.success(vehiclePage);
    }
}