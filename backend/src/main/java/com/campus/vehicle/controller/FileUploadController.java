package com.campus.vehicle.controller;

import com.campus.vehicle.service.FileUploadService;
import com.campus.vehicle.utils.JwtUtil;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/upload")
public class FileUploadController {

    @Autowired
    private FileUploadService fileUploadService;
    
    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 上传头像
     */
    @PostMapping("/avatar")
    public Result<String> uploadAvatar(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        Long userId = getCurrentUserId(request);
        String avatarUrl = fileUploadService.uploadAvatar(userId, file);
        return Result.success(avatarUrl);
    }

    /**
     * 通用图片上传
     */
    @PostMapping("/image")
    public Result<String> uploadImage(@RequestParam("file") MultipartFile file) {
        String imageUrl = fileUploadService.uploadImage(file);
        return Result.success(imageUrl);
    }

    /**
     * 从Token中获取用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            return jwtUtil.getUserIdFromToken(token);
        }
        throw new RuntimeException("无法获取用户ID");
    }
}