package com.campus.vehicle.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.entity.FaultReport;
import com.campus.vehicle.enums.FaultReportStatus;
import com.campus.vehicle.service.FaultReportService;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.campus.vehicle.annotation.RequireAdmin;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/fault-reports")
public class AdminFaultReportController {

    @Autowired
    private FaultReportService faultReportService;

    /**
     * 分页获取故障报告列表
     */
    @GetMapping
    @RequireAdmin
    public Result<Page<FaultReport>> getFaultReportList(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        Page<FaultReport> faultReportPage = faultReportService.getFaultReportList(current, size, status);
        return Result.success(faultReportPage);
    }

    /**
     * 更新故障报告状态
     */
    @PutMapping("/{id}/status")
    @RequireAdmin
    public Result<String> updateFaultReportStatus(@PathVariable Long id,
                                                @RequestParam FaultReportStatus status) {
        faultReportService.updateFaultReportStatus(id, status);
        return Result.success("故障报告状态更新成功");
    }

    /**
     * 获取故障报告详情
     */
    @GetMapping("/{id}")
    @RequireAdmin
    public Result<FaultReport> getFaultReport(@PathVariable Long id) {
        FaultReport faultReport = faultReportService.getById(id);
        return Result.success(faultReport);
    }
}