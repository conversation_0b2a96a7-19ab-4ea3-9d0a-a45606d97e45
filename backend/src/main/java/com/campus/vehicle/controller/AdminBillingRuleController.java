package com.campus.vehicle.controller;

import com.campus.vehicle.annotation.OperationLog;
import com.campus.vehicle.entity.BillingRule;
import com.campus.vehicle.service.BillingRuleService;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.campus.vehicle.annotation.RequireAdmin;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/billing-rule")
public class AdminBillingRuleController {

    @Autowired
    private BillingRuleService billingRuleService;

    /**
     * 获取当前计费规则
     */
    @GetMapping
    @RequireAdmin
    public Result<BillingRule> getCurrentBillingRule() {
        BillingRule billingRule = billingRuleService.getCurrentBillingRule();
        return Result.success(billingRule);
    }

    /**
     * 更新计费规则
     */
    @PutMapping
    @RequireAdmin
    @OperationLog("更新计费规则")
    public Result<String> updateBillingRule(@RequestBody @Validated BillingRule billingRule) {
        billingRuleService.updateBillingRule(billingRule);
        return Result.success("计费规则更新成功");
    }
}