package com.campus.vehicle.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.annotation.OperationLog;
import com.campus.vehicle.dto.IssueCouponRequest;
import com.campus.vehicle.entity.Coupon;
import com.campus.vehicle.service.CouponService;
import com.campus.vehicle.service.AdminCouponService;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.campus.vehicle.annotation.RequireAdmin;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/coupons")
public class AdminCouponController {

    @Autowired
    private AdminCouponService adminCouponService;
    
    @Autowired
    private CouponService couponService;

    /**
     * 获取优惠券模板列表
     */
    @GetMapping
    @RequireAdmin
    public Result<Page<Coupon>> getCouponList(@RequestParam(defaultValue = "1") int current,
                                             @RequestParam(defaultValue = "10") int size) {
        Page<Coupon> couponPage = adminCouponService.getCouponList(current, size);
        return Result.success(couponPage);
    }

    /**
     * 创建优惠券模板
     */
    @PostMapping
    @RequireAdmin
    @OperationLog("创建优惠券模板")
    public Result<String> createCoupon(@RequestBody @Validated Coupon coupon) {
        adminCouponService.createCoupon(coupon);
        return Result.success("优惠券模板创建成功");
    }

    /**
     * 更新优惠券模板
     */
    @PutMapping("/{id}")
    @RequireAdmin
    @OperationLog("更新优惠券模板")
    public Result<String> updateCoupon(@PathVariable Long id, 
                                      @RequestBody @Validated Coupon coupon) {
        coupon.setId(id);
        adminCouponService.updateCoupon(coupon);
        return Result.success("优惠券模板更新成功");
    }

    /**
     * 删除优惠券模板
     */
    @DeleteMapping("/{id}")
    @RequireAdmin
    @OperationLog("删除优惠券模板")
    public Result<String> deleteCoupon(@PathVariable Long id) {
        adminCouponService.deleteCoupon(id);
        return Result.success("优惠券模板删除成功");
    }

    /**
     * 给用户发放优惠券
     */
    @PostMapping("/issue")
    @RequireAdmin
    @OperationLog("发放优惠券")
    public Result<String> issueCoupon(@RequestBody @Validated IssueCouponRequest request) {
        couponService.issueCoupon(request.getUserId(), request.getCouponId());
        return Result.success("优惠券发放成功");
    }

    /**
     * 批量发放优惠券
     */
    @PostMapping("/issue/batch")
    @RequireAdmin
    @OperationLog("批量发放优惠券")
    public Result<String> batchIssueCoupon(@RequestBody @Validated IssueCouponRequest request) {
        adminCouponService.batchIssueCoupon(request.getUserIds(), request.getCouponId());
        return Result.success("批量发放优惠券成功");
    }
}