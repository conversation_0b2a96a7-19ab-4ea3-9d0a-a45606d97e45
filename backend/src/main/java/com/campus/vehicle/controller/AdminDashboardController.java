package com.campus.vehicle.controller;

import com.campus.vehicle.service.AdminDashboardService;
import com.campus.vehicle.vo.DashboardStatsResponse;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.campus.vehicle.annotation.RequireAdmin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/dashboard")
public class AdminDashboardController {

    @Autowired
    private AdminDashboardService adminDashboardService;

    /**
     * 获取仪表盘统计数据
     */
    @GetMapping("/stats")
    @RequireAdmin
    public Result<DashboardStatsResponse> getDashboardStats() {
        DashboardStatsResponse stats = adminDashboardService.getDashboardStats();
        return Result.success(stats);
    }
}