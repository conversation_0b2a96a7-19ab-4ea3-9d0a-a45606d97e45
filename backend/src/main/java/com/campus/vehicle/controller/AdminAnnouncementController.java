package com.campus.vehicle.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.dto.AnnouncementRequest;
import com.campus.vehicle.entity.Announcement;
import com.campus.vehicle.service.AnnouncementService;
import com.campus.vehicle.utils.JwtUtil;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.campus.vehicle.annotation.RequireAdmin;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/admin/announcements")
public class AdminAnnouncementController {

    @Autowired
    private AnnouncementService announcementService;
    
    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 分页获取公告列表
     */
    @GetMapping
    @RequireAdmin
    public Result<Page<Announcement>> getAnnouncementList(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        Page<Announcement> announcementPage = announcementService.getAnnouncementList(current, size);
        return Result.success(announcementPage);
    }

    /**
     * 创建公告
     */
    @PostMapping
    @RequireAdmin
    public Result<String> createAnnouncement(@RequestBody @Validated AnnouncementRequest request,
                                           HttpServletRequest httpRequest) {
        Long authorId = getCurrentUserId(httpRequest);
        announcementService.createAnnouncement(authorId, request);
        return Result.success("创建公告成功");
    }

    /**
     * 更新公告
     */
    @PutMapping("/{id}")
    @RequireAdmin
    public Result<String> updateAnnouncement(@PathVariable Long id,
                                           @RequestBody @Validated AnnouncementRequest request) {
        announcementService.updateAnnouncement(id, request);
        return Result.success("更新公告成功");
    }

    /**
     * 删除公告
     */
    @DeleteMapping("/{id}")
    @RequireAdmin
    public Result<String> deleteAnnouncement(@PathVariable Long id) {
        announcementService.deleteAnnouncement(id);
        return Result.success("删除公告成功");
    }

    /**
     * 获取公告详情
     */
    @GetMapping("/{id}")
    @RequireAdmin
    public Result<Announcement> getAnnouncement(@PathVariable Long id) {
        Announcement announcement = announcementService.getById(id);
        return Result.success(announcement);
    }

    /**
     * 从Token中获取用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            return jwtUtil.getUserIdFromToken(token);
        }
        throw new RuntimeException("无法获取用户ID");
    }
}