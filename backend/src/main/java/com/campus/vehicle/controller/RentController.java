package com.campus.vehicle.controller;

import com.campus.vehicle.dto.EndRentRequest;
import com.campus.vehicle.dto.StartRentRequest;
import com.campus.vehicle.entity.Order;
import com.campus.vehicle.exception.BusinessException;
import com.campus.vehicle.service.RentService;
import com.campus.vehicle.utils.JwtUtil;
import com.campus.vehicle.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/rent")
public class RentController {

    @Autowired
    private RentService rentService;
    
    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 开始租车
     */
    @PostMapping("/start")
    public Result<Order> startRent(@RequestBody @Validated StartRentRequest request,
                                   HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        log.info("用户{}开始租车，车辆ID: {}", userId, request.getVehicleId());
        
        try {
            Order order = rentService.startRent(userId, request);
            log.info("用户{}租车成功，订单ID: {}, 车辆ID: {}", userId, order.getId(), request.getVehicleId());
            return Result.success("租车成功", order);
        } catch (Exception e) {
            log.error("用户{}租车失败，车辆ID: {}, 错误: {}", userId, request.getVehicleId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 结束租车
     */
    @PostMapping("/end")
    public Result<String> endRent(@RequestBody @Validated EndRentRequest request,
                                HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        log.info("用户{}请求结束租车，订单ID: {}, 优惠券ID: {}", userId, request.getOrderId(), request.getUserCouponId());
        
        try {
            rentService.endRent(userId, request);
            log.info("用户{}还车成功，订单ID: {}", userId, request.getOrderId());
            return Result.success("还车成功");
        } catch (Exception e) {
            log.error("用户{}还车失败，订单ID: {}, 错误: {}", userId, request.getOrderId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取当前租车订单
     */
    @GetMapping("/current")
    public Result<Order> getCurrentRentOrder(HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        log.debug("用户{}查询当前租车订单", userId);
        
        Order order = rentService.getCurrentRentOrder(userId);
        if (order != null) {
            log.debug("用户{}当前有租车订单: {}", userId, order.getId());
        } else {
            log.debug("用户{}当前无租车订单", userId);
        }
        return Result.success(order);
    }

    /**
     * 从Token中获取用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            try {
                Long userId = jwtUtil.getUserIdFromToken(token);
                log.debug("从Token中获取用户ID: {}", userId);
                return userId;
            } catch (Exception e) {
                log.error("解析Token失败: {}", e.getMessage());
                throw new BusinessException("用户身份验证失败");
            }
        }
        log.warn("请求头中缺少有效的Authorization Token");
        throw new BusinessException("用户身份验证失败");
    }
}