package com.campus.vehicle.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.campus.vehicle.entity.OperationLog;
import com.campus.vehicle.service.OperationLogService;
import com.campus.vehicle.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.campus.vehicle.annotation.RequireAdmin;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/operation-logs")
public class AdminOperationLogController {

    @Autowired
    private OperationLogService operationLogService;

    /**
     * 分页获取操作日志
     */
    @GetMapping
    @RequireAdmin
    public Result<Page<OperationLog>> getOperationLogs(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String operationType) {
        Page<OperationLog> operationLogPage = operationLogService.getOperationLogs(current, size, operationType);
        return Result.success(operationLogPage);
    }
}