server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: campus-vehicle-rental
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************
    username: root
    password: root
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 10MB
      enabled: true
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/*.xml

# JWT配置
jwt:
  secret: campus-vehicle-rental-secret-key-2024
  expiration: 604800 # 7天

# 支付宝配置（沙箱环境）
alipay:
  app-id: 9021000142612345  # 请替换为你的沙箱应用ID
  # 商户私钥（示例，请替换为你的实际私钥）
  merchant-private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKBxEqj0nXFy9xfjRzfGLDBzthRGnBv
  # 支付宝公钥（示例，请替换为你的实际公钥）  
  alipay-public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuVSU1L7VLVNgU1L7VLPHCgcRKo9J1xcvcX40c3xiwwc7YURpwb
  sign-type: RSA2
  charset: utf-8
  format: json
  gateway-url: https://openapi.alipaydev.com/gateway.do  # 沙箱网关
  # 替换为内网穿透的域名
  notify-url: https://abc123.natfrp.cloud/api/payment/alipay/notify

# 日志配置
logging:
  level:
    com.campus.vehicle: debug
    org.springframework.security: debug

# 文件上传配置
file:
  upload:
    path: /Users/<USER>/项目/电动车/uploads/
    max-size: 5242880  # 5MB