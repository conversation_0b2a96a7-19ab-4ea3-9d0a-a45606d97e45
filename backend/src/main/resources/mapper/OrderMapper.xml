<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.campus.vehicle.mapper.OrderMapper">

    <!-- 使用JOIN查询订单列表，避免N+1问题 -->
    <select id="selectOrdersWithUserAndVehicle" resultType="com.campus.vehicle.vo.AdminOrderVO">
        SELECT 
            o.id,
            o.order_sn AS orderSn,
            o.user_id AS userId,
            o.vehicle_id AS vehicleId,
            o.status,
            o.start_time AS startTime,
            o.end_time AS endTime,
            o.duration,
            o.base_fee AS baseFee,
            o.discount,
            o.final_fee AS finalFee,
            o.create_time AS createTime,
            u.username,
            u.phone AS userPhone,
            v.vehicle_sn AS vehicleCode
        FROM orders o 
        LEFT JOIN users u ON o.user_id = u.id 
        LEFT JOIN vehicles v ON o.vehicle_id = v.id 
        WHERE o.deleted = 0
        <if test="status != null and status != ''">
            AND o.status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND o.order_sn LIKE CONCAT('%', #{keyword}, '%')
        </if>
        ORDER BY o.create_time DESC 
        LIMIT #{offset}, #{size}
    </select>

</mapper>